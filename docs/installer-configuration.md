# 安装程序配置说明

## Windows (NSIS)

### ✅ 你的配置是正确的！

```javascript
nsis: {
  oneClick: false,                    // 禁用一键安装，显示安装向导
  allowToChangeInstallationDirectory: true,  // 允许用户选择安装路径
  allowElevation: true,               // 允许提升权限
  createDesktopShortcut: true,        // 创建桌面快捷方式
  createStartMenuShortcut: true,      // 创建开始菜单快捷方式
}
```

### 功能说明：
- **自定义安装路径**：用户可以选择安装到任意目录
- **安装向导**：显示完整的安装界面，而不是一键安装
- **快捷方式**：自动创建桌面和开始菜单快捷方式
- **权限提升**：需要时自动请求管理员权限

## macOS (DMG)

### 配置特点：
```javascript
dmg: {
  title: '${productName} ${version}',
  background: 'buildResources/dmg-background.png',
  contents: [
    { x: 130, y: 220 },              // 应用图标位置
    { x: 410, y: 220, type: 'link', path: '/Applications' }  // Applications 文件夹链接
  ]
}
```

### 安装体验：
- **拖拽安装**：用户将应用拖拽到 Applications 文件夹
- **自定义背景**：美观的 DMG 背景图
- **标准位置**：默认安装到 `/Applications/ClipNest.app`
- **用户可选择**：用户可以拖拽到任意位置

## Linux (DEB/AppImage)

### DEB 包配置：
```javascript
deb: {
  priority: 'optional',
  depends: ['gconf2', 'gconf-service', 'libnotify4', ...]  // 系统依赖
}
```

### 安装方式：
- **DEB 包**：通过包管理器安装，自动处理依赖
- **AppImage**：便携式应用，无需安装，直接运行
- **标准路径**：DEB 包安装到 `/opt/ClipNest/` 或 `/usr/local/bin/`

## 各平台安装路径自定义支持

| 平台 | 默认路径 | 用户可自定义 | 说明 |
|------|----------|--------------|------|
| Windows | `C:\Program Files\ClipNest` | ✅ 是 | NSIS 安装向导支持 |
| macOS | `/Applications/ClipNest.app` | ✅ 是 | 用户可拖拽到任意位置 |
| Linux (DEB) | `/opt/ClipNest/` | ❌ 否 | 包管理器控制，标准路径 |
| Linux (AppImage) | 任意位置 | ✅ 是 | 便携式，用户完全控制 |

## 最佳实践建议

### 1. Windows
- ✅ 你的配置已经很好了
- 建议添加卸载程序和文件关联
- 考虑添加安装完成后的启动选项

### 2. macOS
- 需要代码签名以避免 Gatekeeper 警告
- 建议添加公证 (notarization) 流程
- DMG 背景图可以提升用户体验

### 3. Linux
- DEB 包适合 Ubuntu/Debian 用户
- AppImage 适合其他发行版用户
- 考虑添加 RPM 包支持 (CentOS/RHEL)

## 构建命令

```bash
# 构建所有平台
npm run build

# 仅构建 Windows
npm run build -- --win

# 仅构建 macOS  
npm run build -- --mac

# 仅构建 Linux
npm run build -- --linux
```

## 注意事项

1. **图标文件**：确保 `buildResources/` 目录下有对应的图标文件
2. **代码签名**：生产环境建议为所有平台配置代码签名
3. **自动更新**：确保安装路径与自动更新机制兼容
4. **权限**：某些功能可能需要特殊权限配置
