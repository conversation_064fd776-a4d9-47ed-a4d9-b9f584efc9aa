
-- 创建文件夹表
CREATE TABLE folder (
    id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, -- 主键，自增
    parent_id INTEGER DEFAULT 0, -- 父级文件夹id
    deleted_at INT(13) DEFAULT 0, -- 是否删除 0 未删除
    material_type INT(2) DEFAULT 2, -- 文件为1 文件夹为2
    path varchar(255) DEFAULT '', -- 文件夹路径
    title varchar(255) DEFAULT '', -- 文件夹名
    team_id INT(8), -- 团队id
    uid varchar(32) DEFAULT '', -- 用户uid
    updated_at INT(13), -- 更新时间戳
    auto_create INT(2) DEFAULT 1, -- 1手动创建 2选择文件夹创建系统目录
    created_at INT(13) -- 创建时间戳
);

-- 创建文件夹索引
CREATE INDEX idx_folder_parent_id ON folder(parent_id);
CREATE INDEX idx_folder_uid ON folder(uid);
CREATE INDEX idx_folder_team_id ON folder(team_id);
CREATE INDEX idx_folder_deleted_at ON folder(deleted_at);

-- 创建素材文件表
CREATE TABLE material_file (
    id varchar(40) PRIMARY KEY NOT NULL, -- 文件唯一ID
    parent_id INTEGER DEFAULT 0, -- 所属文件夹ID
    team_id INT(8), -- 团队ID
    uid varchar(32) DEFAULT '', -- 用户UID
    material_type INT(2) DEFAULT 1, -- 素材类型
    path varchar(255) DEFAULT '', -- 本地文件路径
    source_path varchar(255) DEFAULT '', -- 原始文件路径
    size INT(10) DEFAULT 0, -- 文件大小（字节）
    duration BIGINT DEFAULT 0, -- 文件时长（毫秒）
    width INT(4) DEFAULT 0, -- 像素宽度
    height INT(4) DEFAULT 0, -- 像素高度
    codec_name varchar(30) DEFAULT '', -- 编码器名称
    codec_type varchar(30) DEFAULT '', -- 编码类型（audio/video）
    bit_rate INT(30), -- 码率
    nb_frames INT(30), -- 总帧数
    cover varchar(255) DEFAULT '', -- 封面图路径
    hash varchar(40) DEFAULT '', -- 文件哈希
    status INT(2) DEFAULT 0, -- 状态：0未上传，1处理中...
    clip_cloud_or_local INT(2) DEFAULT 0, -- 存储位置：0初始，1云端，2本地
    object_oid varchar(32) DEFAULT '', -- 对象ID
    reason text DEFAULT '', -- 失败原因
    title varchar(255) DEFAULT '', -- 文件名
    upload_id INT(13) DEFAULT 0, -- 上传ID
    task_no varchar(32), -- 关联任务编号
    clipinfo text DEFAULT '', -- 裁剪信息(JSON)
    updated_at INT(13), -- 更新时间戳
    deleted_at INT(13) DEFAULT 0, -- 删除时间戳
    created_at INT(13), -- 创建时间戳
    tag_id int(10) default 0 -- 标签ID
);

-- 创建素材文件索引
CREATE INDEX idx_material_file_parent_id ON material_file(parent_id);
CREATE INDEX idx_material_file_uid ON material_file(uid);
CREATE INDEX idx_material_file_team_id ON material_file(team_id);
CREATE INDEX idx_material_file_material_type ON material_file(material_type);
CREATE INDEX idx_material_file_hash ON material_file(hash);
CREATE INDEX idx_material_file_deleted_at ON material_file(deleted_at);
