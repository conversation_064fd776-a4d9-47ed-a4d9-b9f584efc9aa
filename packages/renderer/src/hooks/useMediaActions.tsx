// hooks/useMediaActions.ts
import React, { useMemo } from 'react'
import { Edit, Trash, FolderInput } from 'lucide-react'
import { ResourceSource } from '@/types/resources'
import { MediaAction } from '@/pages/Projects/material/components/MediaItem'

export const useMediaActions = (
  renameItem: (
    type: ResourceSource,
    id: string,
    title: string,
    options: {
      label: string
      headerTitle: string
    },
  ) => void,
  deleteItem: (type: ResourceSource, nodeId: string, label: string) => Promise<void>,
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  return useMemo<MediaAction[]>(
    () => [
      {
        icon: <Edit className="w-4 h-4" />,
        label: '修改',
        onClick: (fileId, fileName) =>
          renameItem(ResourceSource.MEDIA, fileId, fileName, {
            label: '素材名称',
            headerTitle: '素材',
          }),
      },
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: '移动到',
        onClick: fileId => {
          setMoveType(ResourceSource.MEDIA)
          setMoveId(fileId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: '删除',
        onClick: (fileId, fileName) =>
          deleteItem(ResourceSource.MEDIA, fileId, fileName),
      },
    ],
    [renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen],
  )
}
