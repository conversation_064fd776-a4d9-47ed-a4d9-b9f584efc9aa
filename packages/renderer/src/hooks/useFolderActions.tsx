import React, { useMemo } from 'react'
import { Edit, Trash, FolderPlus, FolderInput } from 'lucide-react'
import { ResourceSource } from '@/types/resources'
import { FolderAction } from '@/pages/Projects/material/components/MediaItem'

export const useFolderActions = (
  createItem: (
    type: ResourceSource,
    parentId: string,
    options: {
      label: string
      headerTitle: string
    },
  ) => void,
  renameItem: (
    type: ResourceSource,
    id: string,
    title: string,
    options: {
      label: string
      headerTitle: string
    },
  ) => void,
  deleteItem: (type: ResourceSource, nodeId: string, label: string) => Promise<void>,
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  return useMemo<FolderAction[]>(
    () => [
      {
        icon: <FolderPlus className="w-4 h-4" />,
        label: '新建文件夹',
        onClick: nodeId =>
          createItem(ResourceSource.FOLDER, nodeId, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }),
      },
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: '移动',
        onClick: nodeId => {
          setMoveType(ResourceSource.FOLDER)
          setMoveId(nodeId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Edit className="w-4 h-4" />,
        label: '重命名',
        onClick: (nodeId, _parentId, label) =>
          renameItem(ResourceSource.FOLDER, nodeId, label!, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }),
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: '删除',
        onClick: (nodeId, _parentId, label) => deleteItem(ResourceSource.FOLDER, nodeId, label!),
      },
    ],
    [createItem, renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen],
  )
}