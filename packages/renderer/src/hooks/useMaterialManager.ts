import { useState, useMemo, useCallback, useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useFolderData } from '@/hooks/useFolderData'
import { useQueryMediaList } from '@/hooks/queries/useQueryMaterial'
import { useSelection } from '@/hooks/useSelection'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { isValidFolderId, TreeNode } from '@/components/TreeList'
import { MaterialResource, ResourceSource } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'

export function useMaterialManager({ projectId, initialKeyword }: { projectId: number; initialKeyword?: string }) {
  const queryClient = useQueryClient()

  const [filters, setFilters] = useState<MaterialResource.MaterialMediaParams>({
    projectId,
    folderUuid: '',
    sortField: MaterialResource.SortField.UPLOAD_TIME,
    sortOrder: MaterialResource.SortOrder.ASC,
    createAtRange: [],
    durationRange: [],
    useCountRange: undefined,
    quoteCountRange: undefined,
    keyword: initialKeyword || '',
    resType: undefined,
  })

  // 获取目录和素材数据
  const {
    treeData,
    isSuccess: isTreeSuccess,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    childFolders,
  } = useFolderData(projectId)
  const mediaQueryResult = useQueryMediaList(filters, isTreeSuccess && filters.folderUuid !== '')

  const folderAsMediaItems = useMemo<MaterialResource.Media[]>(() => {
    return childFolders.map(folder => ({
      fileId: folder.id,
      fileName: folder.label,
      folderUuid: folder.raw.parentId,
      childrenFolder: folder.children.length || 0,
      mediaNum: folder.raw.imageCount + folder.raw.videoCount + folder.raw.audioCount,
      resType: 0,
      createTime: folder.raw.createdAt || new Date().toISOString(),
      url: ''
    }))
  }, [childFolders])

  // 文件夹筛选逻辑：根据 filters.keyword 过滤
  const filteredFolders = useMemo(() => {
    if (filters.keyword && filters.keyword.trim() !== '') {
      const keywordLower = filters.keyword.toLowerCase()
      return folderAsMediaItems.filter(item => item.fileName?.toLowerCase().includes(keywordLower))
    }
    return folderAsMediaItems
  }, [filters.keyword, folderAsMediaItems])

  const selection = useSelection({
    mediaList: mediaQueryResult.data,
    folderAsMediaItems: filteredFolders,
    getMediaId: media => media.fileId,
    getFolderId: folder => folder.fileId,
  })

  const { MediaAndFoldersMulitAction } = useItemActions()

  const onRefresh = useCallback(async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] }),
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] }),
    ])
  }, [queryClient])

  // 进入文件夹
  const handleFolderClick = useCallback(
    (folderId: string) => {
      setCurrentFolderId(folderId)
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    },
    [queryClient],
  )

  // 多选-放入回收站/彻底删除
  const handleBatchDelete = async (isPermanent: boolean) => {
    await MediaAndFoldersMulitAction(
      isPermanent ? 'deleteMulit' : 'recycleMulit',
      selection.selectedMediaItems,
      selection.selectedFolderItems,
      () => {
        selection.setSelectedMediaItems(new Set())
        selection.setSelectedFolderItems(new Set())
      },
    )
  }

  // 多选-移动
  const handleMoveConfirm = async (selectedNode: TreeNode, moveType: ResourceSource) => {
    if (moveType === ResourceSource.MULTI_SELECT) {
      await MediaAndFoldersMulitAction(
        'moveMulit',
        selection.selectedMediaItems,
        selection.selectedFolderItems,
        () => {
          selection.setSelectedMediaItems(new Set())
          selection.setSelectedFolderItems(new Set())
        },
        selectedNode.id,
      )
    } else {
      await onRefresh()
    }
  }

  useEffect(() => {
    setFilters(prev => ({ ...prev, folderUuid: currentFolderId }))
  }, [currentFolderId])

  // 当前无选中目录 或 选中的目录在 treeData 中已不存在
  useEffect(() => {
    if (!isTreeSuccess || !treeData || treeData.length === 0) return

    const firstFolderId = treeData[0].id
    if (!currentFolderId || !isValidFolderId(treeData, currentFolderId)) {
      setCurrentFolderId(firstFolderId)
      handleFolderClick(firstFolderId)
    }
  }, [isTreeSuccess, treeData, currentFolderId])

  // 清空选中
  useEffect(() => {
    selection.setSelectedMediaItems(new Set())
    selection.setSelectedFolderItems(new Set())
  }, [currentFolderId])

  return {
    filters,
    setFilters,
    treeData,
    childFolders,
    isTreeSuccess,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    filteredFolders,
    mediaQueryResult,
    selection,
    onRefresh,
    handleBatchDelete,
    handleFolderClick,
    handleMoveConfirm,
  }
}
