/**
 * Timeline Component
 *
 * A complex timeline interface that allows users to manage video overlays through
 * drag-and-drop interactions, splitting, duplicating, and deletion operations.
 * The timeline visualizes overlay positions and durations across video frames.
 */

import React, { FC, useCallback, useEffect, useState } from 'react'
import { clamp } from 'lodash'

import { OverlayType } from '@clipnest/remotion-shared/types'
import { MousePositionIndicator } from './mouse-position-indicator'
import { TimelineGrid } from './timeline-grid'
import { TimelineRuler } from './timeline-ruler'
import { TimelineEmptyState } from './timeline-empty-state'
import { PIXELS_PER_FRAME, SHOW_LOADING_PROJECT_ALERT } from '../../constants'
import { useAssetLoading, useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { TimelineTrackHeader } from './timeline-track'
import { CurrentFrameIndicator } from './current-frame-indicator'
import { LoadingIndicator } from '@/components/LoadingIndicator'
import { Track, TrackType } from '@/modules/video-editor/types'

const LoadingMask: FC = () => {
  const { overlays } = useEditorContext()

  // Effect to handle initial load completion
  const [shouldShowInitialLoader, setShouldShowInitialLoader] = useState(false)

  const {
    isLoadingAssets,
    isInitialLoad,
    setInitialLoadComplete,
  } = useAssetLoading()

  useEffect(() => {
    // Only run the check if we're actually playing
    const hasVideoOverlay = overlays.some(
      overlay => overlay.type === OverlayType.VIDEO,
    )

    if (!shouldShowInitialLoader && hasVideoOverlay && isInitialLoad) {
      setShouldShowInitialLoader(true)
    }

    if (overlays.length > 0 && !isLoadingAssets) {
      setInitialLoadComplete()
    }
  }, [
    overlays,
    isInitialLoad,
    isLoadingAssets,
    shouldShowInitialLoader,
    setInitialLoadComplete,
  ])

  return (
    SHOW_LOADING_PROJECT_ALERT
    && isLoadingAssets
    && isInitialLoad
    && shouldShowInitialLoader && (
      <div
        className="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-[1px] flex items-center justify-center z-50"
        style={{ willChange: 'opacity' }}
      >
        <div
          className="flex items-center gap-2 px-3 py-2 bg-white/90 dark:bg-gray-800/90 rounded-lg shadow-sm ring-1 ring-black/5 dark:ring-white/10"
        >
          <LoadingIndicator />
          <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
            Loading project...
          </span>
        </div>
      </div>
    )
  )
}

export const Timeline: React.FC = () => {
  const {
    tracks,
    durationInFrames,
    setSelectedOverlay,
    videoPlayer: { seekTo }
  } = useEditorContext()

  const {
    zoomScale,
    layout: { totalHeight, rowGap, getTrackHeight },
    handleMouseMove
  } = useTimeline()

  type TypeIndexedTrack = Track & {
    typeIndex?: number
  }

  const { result: typeIndexedTrack } = tracks
    .reduce(
      ({ prevItem, result }, item) => {
        if (prevItem && prevItem.type === item.type && typeof prevItem.typeIndex === 'number') {
          const current = { ...item, typeIndex: prevItem.typeIndex + 1 }

          return {
            result: [...result, current] as TypeIndexedTrack[],
            prevItem: current as TypeIndexedTrack
          }
        }

        const current = {
          ...item,
          typeIndex: item.isGlobalTrack || item.type === TrackType.STORYBOARD
            ? null
            : 1
        }
        return {
          result: [...result, current] as TypeIndexedTrack[],
          prevItem: current as TypeIndexedTrack
        }
      },
      { prevItem: null as (TypeIndexedTrack | null), result: [] as TypeIndexedTrack[] }
    )

  const trackHeadersColumnEl = (
    <div
      className="flex-shrink-0  border-gray-800 bg-gray-900/50 flex flex-col"
    >
      {/* Match TimeMarkers height */}
      <div className="flex-1 bg-gray-100 dark:bg-gray-800/50" />

      {/* Match the grid layout exactly */}
      <div
        className="flex flex-col pb-2"
        style={{ height: totalHeight, rowGap }}
      >
        {typeIndexedTrack.map(({ type, isGlobalTrack, typeIndex }, trackIndex) => (
          <div
            key={`drag-${trackIndex}`}
            style={{
              height: getTrackHeight(trackIndex)
            }}
            className="flex items-center px-2"
          >
            <TimelineTrackHeader
              type={type}
              isGlobalTrack={isGlobalTrack}
              trackIndex={trackIndex}
              typeIndex={typeIndex}
            />
          </div>
        ))}
      </div>
    </div>
  )

  const handleTimelineClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const timelineRect = e.currentTarget.getBoundingClientRect()
      const clickX = e.clientX - timelineRect.left

      // Convert pixel position to frame using the same logic as timeline items: 像素->帧
      const clickedFrame = clickX / (zoomScale * PIXELS_PER_FRAME)

      // Clamp to valid frame range
      const newTime = clamp(clickedFrame, 0, durationInFrames)
      seekTo(newTime)
    },
    [durationInFrames, zoomScale],
  )

  return (
    <div className="flex flex-col">
      <div className="flex relative">
        {trackHeadersColumnEl}

        {/* Timeline Content */}
        <div
          className="relative overflow-x-auto scrollbar-hide flex-1 pr-3 pl-0.5"
          style={{
            scrollbarWidth: 'none' /* Firefox */,
            msOverflowStyle: 'none' /* IE and Edge */,
          }}
        >
          <div
            className="pb-2 relative overflow-x-hidden"
            style={{
              // 在后方预留空间, 以显示各种添加按钮
              width: durationInFrames * 1.1 * PIXELS_PER_FRAME * zoomScale,
              minWidth: '100%',
              willChange: 'width, transform',
              transform: 'translateZ(0)',
            }}
            onMouseMove={handleMouseMove}
            onClick={e => {
              handleTimelineClick(e)
              setSelectedOverlay(null)
            }}
          >
            <div className="relative h-full">
              {/* Timeline header with frame markers */}
              <div className="h-[1.3rem]">
                <TimelineRuler />
              </div>

              <CurrentFrameIndicator />

              <MousePositionIndicator />

              <TimelineGrid />
            </div>
          </div>
        </div>

        <TimelineEmptyState />

        <LoadingMask />
      </div>
    </div>
  )
}
