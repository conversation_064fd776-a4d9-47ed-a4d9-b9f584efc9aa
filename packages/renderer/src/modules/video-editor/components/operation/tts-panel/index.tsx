import React, { use<PERSON><PERSON>back, useRef, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import { useInfiniteQueryTimbreList } from '@/hooks/queries/useQueryTimbre'
import { TimbreResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { Pause, Play, Zap } from 'lucide-react'
import { FormSlider } from '@/modules/video-editor/components/common/form-components'
import { ScriptSceneData } from '@/hooks/queries/useQueryScript'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { OverlayType, SoundOverlay, StoryboardOverlay, TextOverlay } from '@clipnest/remotion-shared/types'
import {
  DEFAULT_OVERLAY,
  DEFAULT_TEXT_OVERLAY_STYLES,
  FPS,
  TEXT_DEFAULT_CLOUD_FONT_SRC
} from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { toast } from 'react-toastify'
import { extractSentencesFromScriptScene } from '@/libs/tools/script'
import { textToSpeech } from '@/modules/video-editor/utils/ai-helper'
import { useOperationPanelStore } from '@/modules/video-editor/stores/useOperationPanelStore'
import { AiModule } from '@/libs/request/api/ai'
import { IndexableTrack, Track, TrackType } from '@/modules/video-editor/types'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { useNarrationTrackHelper } from '@/modules/video-editor/hooks/helpers/useNarrationTrackHelper'

import { TTSErrorReportDialog } from './tts-error-report-dialog'
import { TTSProgressOverlay, TTSProgressState, TTSTaskStatus } from './tts-progress-overlay'
import { cacheManager } from '@/libs/cache/cache-manager'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'

type AudioInfo = {
  durationInFrames: number;
  localUrl: string;
  originalUrl: string;
}

type AudioInfoMap = Map<string, AudioInfo>

type IndexedSentence = {
  text: string;
  sceneIndex: number;
  sentenceIndex: number
}

const useGenerateTextAndAudio = (
  params: Omit<AiModule.TextToSpeechRequestParams, 'text'> | null
) => {
  const { textToSpeechDatasource } = useOperationPanelStore()

  const { generateSpeechForTextOverlay } = useNarrationTrackHelper()
  const { tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()

  // 文本转语音状态
  const [ttsProgressState, setTtsProgressState] = useState<TTSProgressState>({
    visible: false,
    completed: 0,
    total: 0,
    tasks: []
  })

  // 错误报告弹窗状态
  const [errorReportOpen, setErrorReportOpen] = useState(false)
  const [failedTasks, _setFailedTasks] = useState<TTSTaskStatus[]>([])

  // 冲突确认弹窗状态
  const [conflictDialogOpen, setConflictDialogOpen] = useState(false)
  const [pendingTextOverlay, setPendingTextOverlay] = useState<(TextOverlay & { currentTrack: IndexableTrack }) | null>(null)
  const [pendingParams, setPendingParams] = useState<Omit<AiModule.TextToSpeechRequestParams, 'text'> | null>(null)

  const lastIdRef = useRef(generateNewOverlayId(tracks))

  /**
   * 智能更新分镜轨道，保持与现有分镜视频轨道的时间对齐
   *
   * - 处理原有分镜数量大于脚本分镜数量的情况
   * - 保留超出脚本范围的分镜，维持其原有属性
   * - 确保所有分镜都能正确应用时间推移
   */
  const updateStoryboardTrack = useCallback((
    scenes: ScriptSceneData[],
    sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }>
  ): {
    updatedStoryboardTrack: Track,
    storyboardAdjustments: Array<{ index: number, oldDuration: number, newDuration: number, durationShift: number }>
  } => {
    const MINIMUM_DURATION = FPS

    // 获取当前的分镜轨道
    const currentStoryboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
    const currentStoryboards = currentStoryboardTrack?.overlays.filter(o => o.type === OverlayType.STORYBOARD) as StoryboardOverlay[] || []

    // 按场景组织句子和音频信息
    const sceneData = new Map<number, {
      sentences: Array<{ text: string, sentenceIndex: number, audioInfo?: AudioInfo }>
      requiredDuration: number
    }>(
      scenes.map((_, index) => {
        return [index, { sentences: [], requiredDuration: MINIMUM_DURATION }]
      })
    )

    // 填充句子和音频信息
    sentences.forEach(sentence => {
      const sceneInfo = sceneData.get(sentence.sceneIndex)
      if (!sceneInfo) return
      const { audioInfo } = sentence

      const sentenceData = {
        text: sentence.text,
        sentenceIndex: sentence.sentenceIndex,
        audioInfo
      }

      sceneInfo.sentences.push(sentenceData)

      // 计算场景所需时长（取最长的句子时长）
      if (audioInfo) {
        sceneInfo.requiredDuration = Math.max(sceneInfo.requiredDuration, audioInfo.durationInFrames)
      }
    })

    // 分镜时长检测与调整
    const updatedStoryboards: StoryboardOverlay[] = []
    const storyboardAdjustments: Array<{ index: number, oldDuration: number, newDuration: number, durationShift: number }> = []

    // 确定需要处理的分镜总数（取脚本分镜数和原有分镜数的最大值）
    const totalStoryboardCount = Math.max(scenes.length, currentStoryboards.length)

    for (let sceneIndex = 0; sceneIndex < totalStoryboardCount; sceneIndex++) {
      const sceneInfo = sceneData.get(sceneIndex)
      const currentStoryboard = currentStoryboards[sceneIndex]

      if (sceneInfo) {
        // 处理脚本中存在的分镜
        const requiredDuration = sceneInfo.requiredDuration

        if (currentStoryboard) {
          const currentDuration = currentStoryboard.durationInFrames

          // 如果分镜时长不足，延长该分镜的 durationInFrames
          if (currentDuration < requiredDuration) {
            const durationShift = requiredDuration - currentDuration
            storyboardAdjustments.push({
              index: sceneIndex,
              oldDuration: currentDuration,
              newDuration: requiredDuration,
              durationShift
            })
          }

          // 更新分镜，保持原有的 from 时间（时间推移将在后续统一处理）
          updatedStoryboards.push({
            ...currentStoryboard,
            durationInFrames: Math.max(currentDuration, requiredDuration)
          })
        } else {
          // 如果没有对应的分镜，创建新的分镜
          const newStoryboard: StoryboardOverlay = {
            ...DEFAULT_OVERLAY,
            id: lastIdRef.current,
            title: '',
            type: OverlayType.STORYBOARD,
            from: updatedStoryboards.length > 0
              ? updatedStoryboards[updatedStoryboards.length - 1].from + updatedStoryboards[updatedStoryboards.length - 1].durationInFrames
              : 0,
            durationInFrames: requiredDuration,
          }
          lastIdRef.current += 1
          updatedStoryboards.push(newStoryboard)
        }
      } else if (currentStoryboard) {
        // 处理超出脚本范围但在原有轨道中存在的分镜
        // 保留这些分镜，维持其原有的时长和属性
        updatedStoryboards.push({
          ...currentStoryboard
        })
      }
    }

    // 应用时间推移到分镜轨道
    let cumulativeShift = 0
    const finalStoryboards = updatedStoryboards.map((storyboard, index) => {
      // 计算当前分镜需要应用的累积推移
      const currentShift = cumulativeShift

      // 检查当前分镜是否被延长了
      const adjustment = storyboardAdjustments.find(adj => adj.index === index)
      if (adjustment) {
        cumulativeShift += adjustment.durationShift
      }

      return {
        ...storyboard,
        from: storyboard.from + currentShift
      }
    })

    return {
      updatedStoryboardTrack: { type: TrackType.STORYBOARD, overlays: finalStoryboards },
      storyboardAdjustments
    }
  }, [tracks])

  /**
   * 生成口播轨道
   */
  const generateNarrationTracks = useCallback(async (
    scenes: ScriptSceneData[],
    sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }>,
    width: number,
    height: number,
    storyboards: StoryboardOverlay[]
  ): Promise<Track[]> => {
    // 按场景组织句子和音频信息
    const sceneData = new Map<number, {
      sentences: Array<{ text: string, sentenceIndex: number, audioInfo?: AudioInfo }>
    }>(
      scenes.map((_, index) => {
        return [index, { sentences: [] }]
      })
    )

    // 填充句子和音频信息
    sentences.forEach(sentence => {
      const sceneInfo = sceneData.get(sentence.sceneIndex)
      if (!sceneInfo) return
      const { audioInfo } = sentence

      const sentenceData = {
        text: sentence.text,
        sentenceIndex: sentence.sentenceIndex,
        audioInfo
      }

      sceneInfo.sentences.push(sentenceData)
    })

    // 计算需要的口播轨道数量
    const maxSentenceCount = Math.max(
      ...Array
        .from(sceneData.values())
        .map(info => info.sentences.length)
    )

    const font = await cacheManager.font.cacheFont(TEXT_DEFAULT_CLOUD_FONT_SRC)

    // 生成口播轨道
    const narrationTracks: Track[] = []

    for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
      const narrationTrack: Track = {
        type: TrackType.NARRATION,
        overlays: []
      }

      scenes.forEach((_, sceneIndex) => {
        const sceneInfo = sceneData.get(sceneIndex)
        const storyboard = storyboards[sceneIndex]
        if (!sceneInfo || !storyboard) return

        const sentence = sceneInfo.sentences[trackIndex]
        if (!sentence || !sentence.text.trim()) {
          return
        }

        // 创建TextOverlay
        const textDuration = sentence.audioInfo?.durationInFrames || Math.max(FPS, sentence.text.length / 10 * FPS)

        const textOverlay: TextOverlay = {
          ...DEFAULT_OVERLAY,
          id: lastIdRef.current,
          type: OverlayType.TEXT,
          content: sentence.text.trim(),
          src: TEXT_DEFAULT_CLOUD_FONT_SRC,
          from: storyboard.from,
          durationInFrames: textDuration,
          width: width * 0.8,
          height: 100,
          left: width * 0.1,
          top: height * 0.8,
          storyboardIndex: sceneIndex,
          styles: {
            ...DEFAULT_TEXT_OVERLAY_STYLES,
            textAlign: 'center',
          }
        }
        lastIdRef.current += 1

        const { minHeight } = calculateTextRenderInfo(font, textOverlay)
        textOverlay.height = Math.max(textOverlay.height, minHeight)

        narrationTrack.overlays.push(textOverlay)

        // 如果有音频且TTS成功（有原始URL），创建对应的SoundOverlay
        if (sentence.audioInfo && sentence.audioInfo.originalUrl) {
          const soundOverlay: SoundOverlay = {
            id: lastIdRef.current,
            type: OverlayType.SOUND,
            content: sentence.audioInfo.originalUrl,
            src: sentence.audioInfo.originalUrl,
            localSrc: sentence.audioInfo.localUrl,
            durationInFrames: sentence.audioInfo.durationInFrames,
            from: storyboard.from,
            height: 100,
            width: 200,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            storyboardIndex: sceneIndex,
            styles: {
              volume: 1,
            },
          }
          lastIdRef.current += 1

          narrationTrack.overlays.push(soundOverlay)
        }
      })

      narrationTracks.push(narrationTrack)
    }

    console.log(narrationTracks)

    return narrationTracks
  }, [tracks])

  /**
   * 应用时间推移到轨道
   */
  const applyTimeShiftToTracks = useCallback((
    tracks: Track[],
    storyboardAdjustments: Array<{ index: number, oldDuration: number, newDuration: number, durationShift: number }>
  ): Track[] => {
    if (storyboardAdjustments.length === 0) {
      return tracks
    }

    return tracks.map(track => {
      if (track.type === TrackType.STORYBOARD || track.isGlobalTrack) {
        return track
      }

      return {
        ...track,
        overlays: track.overlays.map(overlay => {
          // 对于每个调整，检查是否需要推移该 overlay
          let cumulativeShift = 0

          storyboardAdjustments.forEach(adjustment => {
            // 如果 overlay 在被调整的分镜之后，需要应用时间推移
            if (typeof overlay.storyboardIndex === 'number' && overlay.storyboardIndex > adjustment.index) {
              cumulativeShift += adjustment.durationShift
            }
          })

          return cumulativeShift > 0 ? {
            ...overlay,
            from: overlay.from + cumulativeShift
          } : overlay
        })
      }
    })
  }, [])

  /**
   * 直接使用已处理的音频信息生成轨道
   */
  const generateTracksWithAudioInfo = useCallback(async (
    scenes: ScriptSceneData[],
    allSentences: IndexedSentence[],
    audioInfoMap: AudioInfoMap
  ) => {
    try {
      const { width, height } = getAspectRatioDimensions()

      const sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }> = allSentences
        .map(sentence => ({
          ...sentence,
          audioInfo: audioInfoMap.get(sentence.text)
        }))

      // 智能更新分镜轨道
      const { updatedStoryboardTrack, storyboardAdjustments } = updateStoryboardTrack(scenes, sentences)

      // 生成口播轨道
      const newNarrationTracks = await generateNarrationTracks(scenes, sentences, width, height, updatedStoryboardTrack.overlays as StoryboardOverlay[])

      // 更新轨道
      updateTracks(prevTracks => {
        // 应用时间推移处理
        const adjustedTracks = applyTimeShiftToTracks(prevTracks, storyboardAdjustments)

        // 过滤掉旧的分镜轨道和口播轨道
        const filteredTracks = adjustedTracks.filter(track =>
          track.type !== TrackType.STORYBOARD &&
          track.type !== TrackType.NARRATION
        )

        return [updatedStoryboardTrack, ...newNarrationTracks, ...filteredTracks]
      })

      // 隐藏进度遮罩
      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })

      toast.success(`成功生成 ${audioInfoMap.size} 个语音轨道`)
    } catch (error) {
      console.error('生成音频轨道失败:', error)
      toast.error('生成音频轨道失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
    }
  }, [updateTracks, getAspectRatioDimensions, updateStoryboardTrack, generateNarrationTracks, applyTimeShiftToTracks])

  const generateTextAndAudio = async (
    scenes: ScriptSceneData[],
    params: Omit<AiModule.TextToSpeechRequestParams, 'text'>,
  ) => {
    if (!scenes.length) {
      toast.warning('没有可朗读的脚本内容')
      return
    }

    // 提取所有台词
    const allSentences: IndexedSentence[] = []
    scenes.forEach((scene, sceneIndex) => {
      const sentences = extractSentencesFromScriptScene(scene)
      sentences.forEach((sentence, sentenceIndex) => {
        if (sentence.trim()) {
          allSentences.push({ text: sentence.trim(), sceneIndex, sentenceIndex })
        }
      })
    })

    if (!allSentences.length) {
      toast.warning('没有找到有效的台词内容')
      return
    }

    // 初始化进度状态 - 简化为基于句子数量的进度计算
    setTtsProgressState({
      visible: true,
      completed: 0,
      total: allSentences.length,
      tasks: []
    })

    const audioInfoMap: AudioInfoMap = new Map()
    let completedSentencesCount = 0
    await Promise.all(
      allSentences.map(async sentence => {
        const [result] = await textToSpeech({
          ...params,
          text: sentence.text
        })
        completedSentencesCount++
        setTtsProgressState(prev => ({
          ...prev,
          completed: completedSentencesCount
        }))

        if (result) {
          const { audioUrl, localAudioUrl, durationInFrames } = result
          audioInfoMap.set(sentence.text, {
            originalUrl: audioUrl,
            localUrl: localAudioUrl,
            durationInFrames
          })
        }
      })
    )

    if (completedSentencesCount === 0) {
      toast.error('朗读功能执行失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
      return
    }

    return generateTracksWithAudioInfo(scenes, allSentences, audioInfoMap)
  }

  /**
   * 检测指定 TextOverlay 所在分镜的轨道内是否已存在 SoundOverlay
   */
  const checkSoundOverlayConflict = useCallback((textOverlay: TextOverlay): boolean => {
    if (typeof textOverlay.storyboardIndex !== 'number') {
      return false
    }

    // 查找同一分镜内的所有 SoundOverlay
    const soundOverlaysInSameStoryboard = tracks
      .filter(track => track.type === TrackType.NARRATION)
      .flatMap(track => track.overlays)
      .filter(overlay =>
        overlay.type === OverlayType.SOUND &&
        overlay.storyboardIndex === textOverlay.storyboardIndex
      )
      .filter(overlay => overlay.from + overlay.durationInFrames > textOverlay.from)

    return soundOverlaysInSameStoryboard.length > 0
  }, [tracks])

  const startGenerate = useCallback(async (confirm?: boolean) => {
    if (!params) return

    if (Array.isArray(textToSpeechDatasource) && textToSpeechDatasource.length) {
      return generateTextAndAudio(textToSpeechDatasource, params)
    }

    if ('type' in textToSpeechDatasource && textToSpeechDatasource.type === OverlayType.TEXT) {
      const { currentTrack, ...overlay } = textToSpeechDatasource
      if (confirm === true) {
        return generateSpeechForTextOverlay(overlay, currentTrack, params)
      }

      // 检测冲突
      const hasConflict = checkSoundOverlayConflict(textToSpeechDatasource)

      if (hasConflict) {
        // 存在冲突，显示确认弹窗
        setPendingTextOverlay(textToSpeechDatasource)
        setPendingParams(params)
        setConflictDialogOpen(true)
        return
      }

      // 没有冲突，正常执行
      return generateSpeechForTextOverlay(overlay, currentTrack, params)
    }
  }, [params, textToSpeechDatasource, checkSoundOverlayConflict, generateSpeechForTextOverlay])

  /**
   * 处理确认弹窗的确认操作
   */
  const handleConfirmGenerate = useCallback(async () => {
    if (pendingTextOverlay && pendingParams) {
      const { currentTrack, ...overlay } = pendingTextOverlay
      setConflictDialogOpen(false)
      await generateSpeechForTextOverlay(overlay, currentTrack, pendingParams)
      setPendingTextOverlay(null)
      setPendingParams(null)
    }
  }, [pendingTextOverlay, pendingParams, generateSpeechForTextOverlay])

  /**
   * 处理确认弹窗的取消操作
   */
  const handleCancelGenerate = useCallback(() => {
    setConflictDialogOpen(false)
    setPendingTextOverlay(null)
    setPendingParams(null)
  }, [])

  const ErrorReportDialog = useCallback(() => (
    <TTSErrorReportDialog
      open={errorReportOpen}
      onOpenChange={setErrorReportOpen}
      failedTasks={failedTasks}
      successCount={ttsProgressState.total - failedTasks.length}
      totalCount={ttsProgressState.total}
    />
  ), [errorReportOpen, failedTasks, ttsProgressState])

  const ProgressOverlay = useCallback(() => (
    <TTSProgressOverlay progressState={ttsProgressState} />
  ), [ttsProgressState])

  const ConflictDialog = useCallback(() => (
    <AlertDialog open={conflictDialogOpen} onOpenChange={setConflictDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>配音冲突提示</AlertDialogTitle>
          <AlertDialogDescription>
            所在位置已有配音，继续生成可能会覆盖原有配音，是否继续？
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancelGenerate}>
            取消
          </AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirmGenerate}>
            继续生成
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  ), [conflictDialogOpen, handleConfirmGenerate, handleCancelGenerate])

  return {
    startGenerate,
    ErrorReportDialog,
    ProgressOverlay,
    ConflictDialog
  }
}

export const TextToSpeechPanel: React.FC = () => {
  const [speed, setSpeed] = useState(1)
  const [volume, setVolume] = useState(100)
  const [autoDetectLanguage, setAutoDetectLanguage] = useState(true)
  const [playingVoice, setPlayingVoice] = useState<number | null>(null)
  const [selectedTimbre, setSelectedTimbre] = useState<TimbreResource.Timbre | null>(null)

  const playingAudio = useRef<HTMLAudioElement | null>(null)

  // 获取音色列表
  const timbreQueryResult = useInfiniteQueryTimbreList({ pageSize: 12 })

  const { startGenerate, ErrorReportDialog, ProgressOverlay, ConflictDialog } = useGenerateTextAndAudio(
    selectedTimbre
      ? {
        volume,
        speed,
        voice_id: selectedTimbre.content.voiceId,
        pitch: 0,
      }
      : null
  )

  // 播放音色试听
  const handlePlayVoice = useCallback((timbre: TimbreResource.Timbre) => {
    return (event: React.MouseEvent) => {
      event.stopPropagation()

      if (playingVoice === timbre.id) {
        setPlayingVoice(null)
        if (playingAudio.current) {
          playingAudio.current.pause()
        }
        return
      }

      setPlayingVoice(timbre.id)
      if (timbre.content.url) {
        const audio = new Audio(timbre.content.url)
        playingAudio.current = audio

        audio.play().catch(console.error)
        audio.onended = () => {
          setPlayingVoice(null)
          playingAudio.current = null
        }
      }
    }
  }, [playingVoice])

  // 渲染音色项
  const renderTimbreItem = useCallback((timbre: TimbreResource.Timbre, _index: number) => {
    const isSelected = selectedTimbre?.id === timbre.id
    const isPlaying = playingVoice === timbre.id

    return (
      <div
        key={timbre.id}
        onClick={() => setSelectedTimbre(timbre)}
        className={cn(
          'relative p-3 border rounded-md cursor-pointer transition-all',
          'hover:border-primary/50',
          isSelected && 'border-primary bg-primary/10'
        )}
      >
        <div className="flex items-center justify-between">
          <span
            className={cn(
              'text-sm font-medium truncate',
              isSelected && 'text-primary'
            )}
          >
            {timbre.title}
          </span>

          {timbre.content.url && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayVoice(timbre)}
              className="h-6 w-6 p-0 ml-2 flex-shrink-0"
            >
              {isPlaying ? (
                <Pause className="h-3 w-3" />
              ) : (
                <Play className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>

        {timbre.authorName && (
          <div className="text-xs text-muted-foreground mt-1 truncate">
            {timbre.authorName}
          </div>
        )}
      </div>
    )
  }, [selectedTimbre, playingVoice, handlePlayVoice])

  return (
    <div className="h-full flex flex-col bg-background">
      {/* 标题 */}
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-medium text-foreground">智能配音</h2>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* 语音来源 */}
        <div className="space-y-3 hidden">
          <h3 className="text-sm font-medium text-foreground">语音来源</h3>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="auto-detect"
              checked={autoDetectLanguage}
              onCheckedChange={v => setAutoDetectLanguage(!!v)}
            />
            <Label htmlFor="auto-detect" className="text-sm">自动检测语言</Label>
          </div>
        </div>

        {/* 音色选择 */}
        <div className="space-y-3 flex-1 min-h-0">
          <h3 className="text-sm font-medium text-foreground">音色选择</h3>
          <div className="h-64 border rounded-md">
            <InfiniteResourceList
              queryResult={timbreQueryResult}
              renderItem={renderTimbreItem}
              emptyText="暂无音色"
              loadingText="加载音色中..."
              className="h-full"
              itemsContainerClassName="p-2 grid grid-cols-3 gap-2"
            />
          </div>
          {selectedTimbre && (
            <div className="text-xs text-muted-foreground">
              已选择: {selectedTimbre.title}
            </div>
          )}
        </div>

        {/* 调节参数 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-foreground">调节</h3>

          {/* 语速控制 */}
          <FormSlider
            label="语速"
            value={speed}
            onChange={val => setSpeed(val)}
            min={0.5}
            max={2}
            step={0.1}
            showInput={true}
          />

          {/* 音量控制 */}
          <FormSlider
            label="音量"
            value={volume}
            onChange={val => setVolume(val)}
            min={0}
            max={100}
            step={1}
            showInput={true}
          />
        </div>
      </div>

      {/* 底部操作按钮 */}
      <div className="p-4 border-t border-border">
        <Button
          disabled={!selectedTimbre}
          className="w-full bg-gradient-brand text-white"
          size="lg"
          onClick={() => startGenerate()}
        >
          <Zap className="w-4 h-4 mr-2" />
          一键配音
        </Button>
      </div>

      <ErrorReportDialog />
      <ProgressOverlay />
      <ConflictDialog />
    </div>
  )
}
