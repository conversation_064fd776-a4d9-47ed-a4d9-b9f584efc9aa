import React from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { MediaFilterPresetSelector } from '@/modules/video-editor/components/common/media-filter-preset-selector'
import { useOverlayEditing } from '@/modules/video-editor/contexts'

export const VideoStylePanel = () => {
  const { localOverlay: videoOverlay, updateEditingOverlay } = useOverlayEditing<VideoOverlay>()

  return (
    <div className="space-y-6">
      {/* Appearance Settings */}
      <div className="space-y-4 rounded-md bg-background  p-4 border border-border ">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Appearance
        </h3>

        <div className="space-y-2">
          <label className="text-xs text-gray-600 dark:text-gray-400">
            Fit
          </label>
          <select
            value={videoOverlay?.styles?.objectFit ?? 'cover'}
            onChange={e =>
              updateEditingOverlay({ styles: { objectFit: e.target.value as any } }, true)}
            className="w-full ted/50 border border-border rounded-md text-xs p-2 hover:border-gray-300 dark:hover:border-gray-600 transition-colors text-gray-900 dark:text-gray-100"
          >
            <option value="cover">Cover</option>
            <option value="contain">Contain</option>
            <option value="fill">Fill</option>
          </select>
        </div>

        {/* Filter Preset Selector */}
        <MediaFilterPresetSelector
          localOverlay={videoOverlay}
          handleStyleChange={updateEditingOverlay}
        />

        {/* Border Radius */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs text-gray-600 dark:text-gray-400">
              Border Radius
            </label>
            <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[40px] text-right">
              {videoOverlay?.styles?.borderRadius ?? '0px'}
            </span>
          </div>
          <input
            type="number"
            value={parseInt(videoOverlay?.styles?.borderRadius ?? '0')}
            onChange={e =>
              updateEditingOverlay({ styles: { borderRadius: `${e.target.value}px` } }, true)}
            min="0"
            className="w-full ted/50 border border-border dark:border-gray-700 rounded-md text-xs p-2 hover:border-gray-300 dark:hover:border-gray-600 transition-colors text-gray-900 dark:text-gray-100"
          />
        </div>

      </div>
    </div>
  )
}
