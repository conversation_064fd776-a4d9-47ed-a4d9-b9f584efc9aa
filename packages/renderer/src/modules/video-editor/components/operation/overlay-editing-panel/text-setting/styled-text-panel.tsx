import React, { useCallback } from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { FontStyleResource } from '@/types/resources'

import FontStyleSelector from '@/modules/video-editor/components/common/font-style-selector'
import { useTextSettingContext } from './context'

export const StyledTextPanel = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const handleFontStyleSelect = useCallback(
    async (fontStyleResource: FontStyleResource.FontStyle, previewOverlay: TextOverlay) => {
      if (!textOverlay) return

      try {
        // 使用预览覆盖层的样式更新当前覆盖层
        const updatedOverlay: Partial<TextOverlay> = {
          src: fontStyleResource.content.fontPath,
          styles: {
            ...textOverlay.styles,
            // 更新字体相关样式
            fontFamily: previewOverlay.styles.fontFamily,
            fontStyle: previewOverlay.styles.fontStyle,
            fontWeight: previewOverlay.styles.fontWeight,
            // 更新颜色样式
            color: previewOverlay.styles.color,
            backgroundColor: previewOverlay.styles.backgroundColor,
            // 更新轮廓样式
            strokeEnabled: previewOverlay.styles.strokeEnabled,
            strokeWidth: previewOverlay.styles.strokeWidth,
            strokeColor: previewOverlay.styles.strokeColor,
            // 更新阴影样式
            shadowEnabled: previewOverlay.styles.shadowEnabled,
            shadowDistance: previewOverlay.styles.shadowDistance,
            shadowAngle: previewOverlay.styles.shadowAngle,
            shadowBlur: previewOverlay.styles.shadowBlur,
            shadowColor: previewOverlay.styles.shadowColor,
            shadowOpacity: previewOverlay.styles.shadowOpacity,
            // 更新下划线样式
            underlineEnabled: previewOverlay.styles.underlineEnabled,
          }
        }

        requestUpdateText(updatedOverlay)
      } catch (error) {
        console.error('[花体字编辑] 更新花体字样式失败:', error)
      }
    },
    [textOverlay, requestUpdateText]
  )

  return (
    <div className="h-full flex flex-col">

      <div className="flex-1 overflow-hidden">
        <FontStyleSelector
          onFontStyleSelect={handleFontStyleSelect}
          className="h-full flex flex-wrap"
        />
      </div>
    </div>
  )
}
