import React from 'react'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'

import { GetTypedOverlay, OverlayUpdater } from '@/modules/video-editor/contexts/cached-overlays/context'

export type OverlayEditingContextValues = {
  localOverlay: Overlay,
  updateEditingOverlay<TOverlayType extends OverlayType>(
    overlay: OverlayUpdater<GetTypedOverlay<TOverlayType>>,
    commit?: boolean
  ): void
}

export const OverlayEditingContext = React.createContext<OverlayEditingContextValues>(null as any)

export function useOverlayEditing<TOverlay extends Overlay>() {
  const context = React.useContext(OverlayEditingContext) as OverlayEditingContextValues | null
  if (!context) {
    throw new Error('useOverlaySetting must be used within an OverlaySettingContext')
  }

  const { localOverlay, updateEditingOverlay } = context

  return {
    localOverlay: localOverlay as TOverlay,
    updateEditingOverlay: updateEditingOverlay
  }
}
