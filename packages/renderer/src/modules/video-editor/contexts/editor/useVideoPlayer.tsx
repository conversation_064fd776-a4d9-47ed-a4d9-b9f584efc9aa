import React, { useState, useEffect, useRef, useCallback } from 'react'
import { PlayerRef } from '@remotion/player'
import { FPS } from '../../constants'

export type VideoPlayerHook = {
  /**
   * 每秒帧数
   */
  fps: number

  /**
   * 播放器的当前状态(播放/暂停)
   */
  isPlaying: boolean

  /**
   * 播放器当前播放位置的帧数
   */
  currentFrame: number

  /**
   * 当前播放速率
   */
  playbackRate: number

  /**
   * <VideoPlayer /> 组件的 ref
   */
  playerRef: React.RefObject<any>

  /**
   * 播放/暂停
   */
  togglePlayPause(): void

  /**
   * 调整播放速度倍率
   */
  setPlaybackRate(rate: number): void

  /**
   * 将帧数转换为时间戳
   */
  formatTime(frame: number): string

  /**
   * 跳转到指定帧
   */
  seekTo(frame: number): void
}

/**
 * Custom hook for managing video player functionality
 * @returns An object containing video player controls and state
 */
export const useVideoPlayer = (): VideoPlayerHook => {
  const [fps] = useState(FPS)
  const [isPlaying, setIsPlaying] = useState(false)
  const [playbackRate, setPlaybackRate] = useState(1)
  const [currentFrame, setCurrentFrame] = useState(0)
  const playerRef = useRef<PlayerRef>(null)

  // Frame update effect
  useEffect(() => {
    let animationFrameId: number
    let lastUpdateTime = 0
    const frameInterval = 1000 / fps

    const updateCurrentFrame = () => {
      const now = performance.now()
      if (now - lastUpdateTime >= frameInterval) {
        if (playerRef.current) {
          const frame = Math.round(playerRef.current.getCurrentFrame())
          setCurrentFrame(frame)
        }
        lastUpdateTime = now
      }

      animationFrameId = requestAnimationFrame(updateCurrentFrame)
    }

    // Start the animation frame loop
    animationFrameId = requestAnimationFrame(updateCurrentFrame)

    // Clean up
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
    }
  }, [isPlaying, fps])

  /**
   * Toggles between play and pause states
   */
  const togglePlayPause = useCallback(() => {
    if (playerRef.current) {
      if (!isPlaying) {
        playerRef.current.play()
      }
      else {
        playerRef.current.pause()
      }
      setIsPlaying(!isPlaying)
    }
  }, [playerRef, isPlaying])

  /**
   * Converts frame count to formatted time string
   * @param frames - Number of frames to convert
   * @returns Formatted time string in MM:SS format
   */
  const formatTime = useCallback((frames: number) => {
    const totalSeconds = frames / fps
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = Math.floor(totalSeconds % 60)
    const frames2Digits = Math.floor(frames % fps)
      .toString()
      .padStart(2, '0')

    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}.${frames2Digits}`
  }, [])

  /**
   * Seeks to a specific frame in the video
   * @param frame - Target frame number
   */
  const seekTo = (frame: number) => {
    if (playerRef.current) {
      setCurrentFrame(frame)
      playerRef.current.seekTo(frame)
    }
  }

  return {
    fps,
    isPlaying,
    playbackRate,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    seekTo,
    setPlaybackRate
  }
}
