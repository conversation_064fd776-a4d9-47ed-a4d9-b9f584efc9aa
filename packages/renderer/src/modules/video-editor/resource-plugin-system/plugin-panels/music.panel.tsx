import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { ResourceSource, SoundResource } from '@/types/resources'
import { ResourcePanelLayout, ResourceTab } from '../components/resource-panel-layout'
import { Music } from 'lucide-react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import { AudioResourceItem } from '../components/audio-resource-item'
import {
  useInfiniteQueryLocalMusicList,
  useInfiniteQueryMusicRankList,
  useInfiniteQueryMusicUnified,
  useQueryMusicCategory,
  useQueryMusicDirList,
} from '@/hooks/queries/useQueryMusic'
import { filterByDuration } from '../../components/common/duration-filter'
import { ResourceTabType } from '@/modules/video-editor/resource-plugin-system'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { UploadedFile } from '@/components/ui/file-uploader'
import { ResourceModule } from '@/libs/request/api/resource'
import { LocalResourcePanel } from '@/modules/video-editor/resource-plugin-system/components/local-resource-panel'
import { LocalAudioResourceItem } from '../components/local-audio-item'
import { useAddAudioToTimeline } from '../../hooks/useAddAudioToTimeLine'

const MusicResourceTabs: ResourceTab[] = [
  {
    label: '音乐库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    showDurationFilter: true,
    searchPlaceholder: '搜索音乐，按回车键搜索',
  },
  {
    label: '热门排行榜',
    value: ResourceTabType.RANK,
    showSearch: false,
    showCategorySelector: false,
    showDurationFilter: true,
  },
  {
    label: '我的音乐',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    showDurationFilter: true,
    searchPlaceholder: '搜索本地音乐，按回车键搜索',
  },
]

export function MusicPanel() {
  const { data: musicCategory } = useQueryMusicCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchKey, setSearchKey] = useState<string>('')
  const [activeTab, setActiveTab] = useState<string>(ResourceTabType.ONLINE)
  const [selectedDuration, setSelectedDuration] = useState<string>('')

  const infiniteQueryResult = useInfiniteQueryMusicUnified({
    pageSize: 50,
    selectedCategory: selectedCategory,
    search: searchKey,
    enabled: activeTab === ResourceTabType.ONLINE,
  })

  const infiniteRankQueryResult = useInfiniteQueryMusicRankList({
    pageSize: 50,
    search: searchKey,
    enabled: activeTab === ResourceTabType.RANK,
  })

  //本地资源
  const queryClient = useQueryClient()
  const { data: dirList } = useQueryMusicDirList() // 请求本地目录
  const [currentFolderId, setCurrentFolderId] = useState('') //当前目录
  const { handleAddAudioToTimeline } = useAddAudioToTimeline()

  //更新当前目录
  useEffect(() => {
    if (dirList?.length && !currentFolderId) {
      setCurrentFolderId(dirList[0].id)
    }
  }, [dirList, currentFolderId])
  //刷新目录
  const onRefershLocalResource = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST] })
  }, [queryClient])
  //点击目录回调
  const handleFolderChange = useCallback(
    async (folderId: string) => {
      setCurrentFolderId(folderId)
      await onRefershLocalResource()
    },
    [onRefershLocalResource],
  )

  const renderMusicItem = useCallback(
    (item: SoundResource.Sound, index: number) => {
      return (
        <div className="relative">
          <AudioResourceItem
            key={index}
            item={item}
            icon={<Music className="w-8 h-8" />}
            resourceType={ResourceType.MUSIC}
            customExt="mp3"
            showCollectionButton={true}
          />
        </div>
      )
    },
    [AudioResourceItem],
  )

  const renderMusicContent = useCallback(() => {
    const filteredQueryResult = useMemo(() => {
      if (!infiniteQueryResult.data || !selectedDuration) {
        return infiniteQueryResult
      }

      return {
        ...infiniteQueryResult,
        data: {
          ...infiniteQueryResult.data,
          pages: infiniteQueryResult.data.pages.map(page => ({
            ...page,
            list: filterByDuration(page.list, selectedDuration),
          })),
        },
      }
    }, [infiniteQueryResult, selectedDuration])

    return (
      <InfiniteResourceList
        queryResult={filteredQueryResult}
        renderItem={renderMusicItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-3 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteQueryResult, renderMusicItem, selectedDuration])

  const renderMusicRankContent = useCallback(() => {
    const filteredRankQueryResult = useMemo(() => {
      if (!infiniteRankQueryResult.data || !selectedDuration) {
        return infiniteRankQueryResult
      }

      return {
        ...infiniteRankQueryResult,
        data: {
          ...infiniteRankQueryResult.data,
          pages: infiniteRankQueryResult.data.pages.map(page => ({
            ...page,
            list: filterByDuration(page.list, selectedDuration),
          })),
        },
      }
    }, [infiniteRankQueryResult, selectedDuration])

    return (
      <InfiniteResourceList
        queryResult={filteredRankQueryResult}
        renderItem={renderMusicItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-3 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteRankQueryResult, renderMusicItem, selectedDuration])

  const renderLocalMusicContent = useCallback(() => {
    const { data: localResources } = useInfiniteQueryLocalMusicList({
      pageSize: 30,
      folderUuid: currentFolderId,
      search: searchKey,
    })

    //上传文件回调
    const handleUploadComplete = useCallback(async (uploaded: UploadedFile[]) => {
      console.log('执行创建')

      // 上传成功的文件数组
      for (const file of uploaded) {
        try {
          await ResourceModule.music.localCreate({
            folderUuid: file.folderUuid,
            title: file.fileName || file.file.name,
            fileMd5: file.fileMd5 ?? '',
            contentType: 'music',
            objectId: file.objectId ?? '',
          })
          console.log('文件已成功上传并创建！')
        } catch (err) {
          console.error('上传失败：', err)
        }
      }

      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST] })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_MUSIC_LIST] })
    }, [])
    return (
      <LocalResourcePanel<SoundResource.SoundLocal>
        dirList={dirList || []}
        currentFolderId={currentFolderId}
        onFolderChange={handleFolderChange}
        resourceType={ResourceSource.LOCAL_MUSIC}
        resourceFolderType={ResourceSource.LOCAL_MUSIC_FOLDER}
        fileUploadTypes={['audio/mpeg']}
        searchKey={searchKey}
        onAddToTimeline={() => handleAddAudioToTimeline}
        resources={localResources}
        onUploadComplete={handleUploadComplete}
        emptyText="暂无本地音乐"
        renderResourceItem={(resource: SoundResource.SoundLocal) => (
          <LocalAudioResourceItem
            key={resource.fileId}
            audioUrl={resource.content.itemUrl}
            durations={resource.content.durationMsec}
            title={resource.title}
            id={resource.fileId}
            icon={<Music className="w-8 h-8" />}
            resourceType={ResourceType.MUSIC}
            customExt="mp3"
          />
        )}
      />
    )
  }, [dirList, currentFolderId, handleFolderChange, handleAddAudioToTimeline])

  const hasMusic = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    if (!firstPage) return false

    // 如果有时长筛选，检查筛选后的结果
    if (selectedDuration) {
      const filteredList = filterByDuration(firstPage.list, selectedDuration)
      return filteredList.length > 0
    }

    return firstPage.list.length > 0
  }, [infiniteQueryResult.data, selectedDuration])

  const hasRankMusic = useMemo(() => {
    const firstPage = infiniteRankQueryResult.data?.pages?.[0]
    if (!firstPage) return false

    if (selectedDuration) {
      const filteredList = filterByDuration(firstPage.list, selectedDuration)
      return filteredList.length > 0
    }

    return firstPage.list.length > 0
  }, [infiniteRankQueryResult.data, selectedDuration])

  const tabContentRenderers = useMemo(
    () => ({
      [ResourceTabType.ONLINE]: renderMusicContent,
      [ResourceTabType.RANK]: renderMusicRankContent,
      [ResourceTabType.LOCAL]: renderLocalMusicContent,
    }),
    [renderMusicContent, renderMusicRankContent, renderLocalMusicContent],
  )

  // 空状态配置映射
  const emptyStateConfig = useMemo(
    () => ({
      [ResourceTabType.ONLINE]: {
        isEmpty: !hasMusic,
        emptyText: '该分类暂无音乐',
      },
      [ResourceTabType.RANK]: {
        isEmpty: !hasRankMusic,
        emptyText: '暂无热门音乐',
      },
      [ResourceTabType.LOCAL]: {
        isEmpty: false, // 本地资源暂时不检查空状态
        emptyText: '暂无本地音乐',
      },
    }),
    [hasMusic, hasRankMusic],
  )

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return MusicResourceTabs.map(tab => {
      const renderer = tabContentRenderers[tab.value as keyof typeof tabContentRenderers]
      const emptyState = emptyStateConfig[tab.value as keyof typeof emptyStateConfig]

      return {
        ...tab,
        renderContent: renderer || (() => null),
        isEmpty: emptyState?.isEmpty || false,
        emptyText: emptyState?.emptyText || '暂无数据',
        showDurationFilter: tab.value === ResourceTabType.ONLINE,
      }
    })
  }, [tabContentRenderers, emptyStateConfig])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={musicCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={setSelectedCategory}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
      onTabChange={setActiveTab}
      selectedDuration={selectedDuration}
      onDurationChange={setSelectedDuration}
    />
  )
}

export default memo(MusicPanel)
