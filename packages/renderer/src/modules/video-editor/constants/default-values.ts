import { Overlay, OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { FPS } from './basic-configs'
import { Track, TrackType } from '@/modules/video-editor/types'

const OVERLAY_PLACEHOLDER: Omit<Overlay, 'type'> = {
  id: 0,
  left: 0,
  top: 0,
  width: 0,
  height: 0,
  from: 0,
  durationInFrames: 0,
  isDragging: false,
  rotation: 0
}

export const TEXT_DEFAULT_CLOUD_FONT_SRC = 'https://clipnest-library.oss-cn-shanghai.aliyuncs.com/resources/fonts/file_v2/6e4bee66c618418c9c8a23578e5bd468.ttf'
export const TEXT_DEFAULT_CLOUD_FONT_NAME = '阿里妈妈数黑体'

export const DEFAULT_TEXT_OVERLAY_STYLES: TextOverlay['styles'] = {
  fontSize: 100,
  fontWeight: 'bold',
  color: 'white',
  backgroundColor: 'transparent',
  fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME,
  fontStyle: 'normal',
  underlineEnabled: false,
  lineSpacing: 0.2,
  textAlign: 'center',
  letterSpacing: 0,

  // 描边属性
  strokeEnabled: false,
  strokeWidth: 1,
  strokeColor: '#000000',

  // 阴影属性
  shadowEnabled: false,
  shadowDistance: 3,
  shadowAngle: 45,
  shadowBlur: 2,
  shadowColor: '#000000',
  shadowOpacity: 0.5,

  // 透明度
  textOpacity: 1,
  backgroundOpacity: '1',

  // 布局属性
  padding: 0.05, // 默认两侧各留5%空隙

  // 基础样式
  opacity: 1,
  zIndex: 1,
  transform: 'none'
}

export const DEFAULT_OVERLAY: Pick<
  Overlay,
  'width' | 'height' | 'left' | 'top' | 'isDragging' | 'rotation' | 'durationInFrames'
> = {
  width: 0,
  height: 0,
  left: 0,
  top: 0,
  isDragging: false,
  rotation: 0,
  durationInFrames: FPS * 3
} as const

export const DEFAULT_TRACKS: Track[] = [
  {
    type: TrackType.STORYBOARD,
    overlays: [
      {
        ...OVERLAY_PLACEHOLDER,
        id: 0,
        type: OverlayType.STORYBOARD,
        from: 0,
        durationInFrames: 90,
        title: '第一个分镜',
      },
      {
        ...OVERLAY_PLACEHOLDER,
        id: 1,
        type: OverlayType.STORYBOARD,
        from: 90,
        durationInFrames: 90,
        title: '第2个分镜',
      },
      {
        ...OVERLAY_PLACEHOLDER,
        id: 2,
        type: OverlayType.STORYBOARD,
        from: 180,
        durationInFrames: 90,
        title: '第3个分镜',
      },
      {
        ...OVERLAY_PLACEHOLDER,
        id: 3,
        type: OverlayType.STORYBOARD,
        from: 270,
        durationInFrames: 270,
        title: '第4个分镜',
      },
      {
        ...OVERLAY_PLACEHOLDER,
        id: 4,
        type: OverlayType.STORYBOARD,
        from: 540,
        durationInFrames: 270,
        title: '第五个分镜',
      },
    ]
  },

  {
    type: TrackType.VIDEO,
    overlays: [
      {
        id: 2001,
        left: 0,
        storyboardIndex: 0,
        top: 0,
        width: 1440,
        height: 1920,
        from: 0,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: '',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/1.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 120,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        },
      },
      {
        id: 2002,
        left: 0,
        storyboardIndex: 1,
        top: 0,
        width: 1440,
        height: 1920,
        from: 90,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: '',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/2.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 30,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      },
      {
        id: 2003,
        left: 0,
        storyboardIndex: 2,
        top: 0,
        width: 1440,
        height: 1920,
        from: 180,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: '',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/3.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 120,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      },
      {
        id: 2004,
        left: 0,
        storyboardIndex: 3,
        top: 0,
        width: 1440,
        height: 1920,
        from: 270,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: 'https://images.pexels.com/videos/3044090/free-video-3044090.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/4.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 120,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      },
      {
        id: 2005,
        left: 0,
        storyboardIndex: 3,
        top: 0,
        width: 1440,
        height: 1920,
        from: 360,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: 'https://images.pexels.com/videos/3044090/free-video-3044090.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/5.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 120,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      },
      {
        id: 2006,
        left: 0,
        storyboardIndex: 4,
        top: 0,
        width: 1440,
        height: 1920,
        from: 540,
        durationInFrames: 60,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: 'https://images.pexels.com/videos/3044090/free-video-3044090.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/6.mp4',
        originalDurationInFrames: 120,
        videoStartTime: 0,
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          padding: 5,
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      },
      {
        id: 2007,
        left: 400,
        storyboardIndex: 4,
        top: 0,
        width: 680,
        height: 1920,
        from: 600,
        durationInFrames: 90,
        rotation: 0,
        isDragging: false,
        type: OverlayType.VIDEO,
        content: 'https://images.pexels.com/videos/3044090/free-video-3044090.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
        src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/7.mp4',
        videoStartTime: 0,
        originalDurationInFrames: 120,
        cropData: {
          'x': 19.2,
          'y': 20,
          'width': 36.71,
          'height': 62.8,
          'unit': '%'
        },
        styles: {
          opacity: 1,
          zIndex: 100,
          transform: 'none',
          objectFit: 'cover',
          // padding: '50px',
          paddingBackgroundColor: '#ffffff',
          filter: '',
          volume: 1
        }
      }
    ]
  },
  /*
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2005,
         left: 0,
         storyboardIndex: 0,
         top: 0,
         width: 1440,
         height: 1920,
         from: 0,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/5.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2006,
         left: 0,
         storyboardIndex: 1,
         top: 0,
         width: 1440,
         height: 1920,
         from: 90,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/6.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2007,
         left: 0,
         storyboardIndex: 2,
         top: 0,
         width: 1440,
         height: 1920,
         from: 180,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/7.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2008,
         left: 0,
         storyboardIndex: 3,
         top: 0,
         width: 1440,
         height: 1920,
         from: 270,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: 'https://images.pexels.com/videos/3044090/free-video-3044090.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/8.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       }
     ]
   },
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2009,
         left: 0,
         storyboardIndex: 0,
         top: 0,
         width: 1440,
         height: 1920,
         from: 0,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/9.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2010,
         left: 0,
         storyboardIndex: 1,
         top: 0,
         width: 1440,
         height: 1920,
         from: 90,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/10.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2011,
         left: 0,
         storyboardIndex: 2,
         top: 0,
         width: 1440,
         height: 1920,
         from: 180,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/11.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
       {
         id: 2012,
         left: 0,
         storyboardIndex: 3,
         top: 0,
         width: 1440,
         height: 1920,
         from: 270,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/12.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       }
     ]
   },
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2013,
         left: 0,
         storyboardIndex: 0,
         top: 0,
         width: 1440,
         height: 1920,
         from: 0,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/13.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
     ]
   },
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2016,
         left: 0,
         storyboardIndex: 3,
         top: 0,
         width: 1440,
         height: 1920,
         from: 270,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/16.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       }
     ],
   },
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2015,
         left: 0,
         storyboardIndex: 2,
         top: 0,
         width: 1440,
         height: 1920,
         from: 180,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/15.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
     ],
   },
   {
     type: TrackType.VIDEO,
     overlays: [],
   },
   {
     type: TrackType.VIDEO,
     overlays: [
       {
         id: 2014,
         left: 0,
         storyboardIndex: 1,
         top: 0,
         width: 1440,
         height: 1920,
         from: 90,
         durationInFrames: 90,
         rotation: 0,
         isDragging: false,
         type: OverlayType.VIDEO,
         content: '',
         src: 'https://dev-common-1304320301.cos.ap-guangzhou.myqcloud.com/tmp/%E5%90%91%E6%97%A5%E8%91%B5%E6%8B%96%E6%8A%8A/14.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter: '',
           volume: 1
         }
       },
     ],
   },
   */

  {
    type: TrackType.NARRATION,
    overlays: [
      {
        ...DEFAULT_OVERLAY,
        id: 957242,
        storyboardIndex: 0,
        type: OverlayType.SOUND,
        content: 'Another Lowfi',
        src: 'https://rwxrdxvxndclnqvznxfj.supabase.co/storage/v1/object/public/sounds/sound-3.mp3?t=2024-11-04T03%3A52%3A35.101Z',
        from: 0,
        durationInFrames: 90,
        width: 1920,
        height: 100,
        styles: {
          opacity: 1,
        },
      },
      {
        ...DEFAULT_OVERLAY,
        id: 900202,
        type: OverlayType.SOUND,
        storyboardIndex: 1,
        content: 'Another Lowfi',
        src: 'https://rwxrdxvxndclnqvznxfj.supabase.co/storage/v1/object/public/sounds/sound-3.mp3?t=2024-11-04T03%3A52%3A35.101Z',
        from: 90,
        durationInFrames: 30,
        width: 1920,
        height: 100,
        styles: {
          opacity: 1,
        },
      },
      {
        ...DEFAULT_OVERLAY,
        id: 9002088,
        type: OverlayType.SOUND,
        storyboardIndex: 1,
        content: 'Another Lowfi',
        src: 'https://rwxrdxvxndclnqvznxfj.supabase.co/storage/v1/object/public/sounds/sound-3.mp3?t=2024-11-04T03%3A52%3A35.101Z',
        from: 120,
        durationInFrames: 40,
        width: 1920,
        height: 100,
        styles: {
          opacity: 1,
        },
      },

      {
        ...DEFAULT_OVERLAY,
        id: 957201,
        storyboardIndex: 0,
        type: OverlayType.TEXT,
        left: 24,
        top: 127,
        width: 1195,
        height: 444,
        from: 10,
        durationInFrames: 60,
        content: '观众朋友们大家好',
        src: '',
        styles: {
          fontSize: 40,
          fontWeight: 'bold',
          color: 'rgba(255, 169, 2, 1)',
          backgroundColor: '',
          fontFamily: 'font-league-spartan',
          fontStyle: 'normal',
          textAlign: 'center',
          letterSpacing: 0,
          opacity: 1,
          zIndex: 1,
          transform: 'none'
        }
      },
      {
        ...DEFAULT_OVERLAY,
        id: 900201,
        type: OverlayType.TEXT,
        storyboardIndex: 1,
        left: 24,
        top: 127,
        width: 1195,
        height: 444,
        from: 90,
        durationInFrames: 40,
        content: '欢迎收听',
        src: '',
        styles: {
          fontSize: 40,
          fontWeight: 'bold',
          color: 'rgba(255, 169, 2, 1)',
          backgroundColor: '',
          fontFamily: 'font-league-spartan',
          fontStyle: 'normal',
          textAlign: 'center',
          letterSpacing: 0,
          opacity: 1,
          zIndex: 1,
          transform: 'none'
        }
      },
      {
        ...DEFAULT_OVERLAY,
        id: 900203,
        storyboardIndex: 1,
        type: OverlayType.TEXT,
        left: 24,
        top: 127,
        width: 1195,
        height: 444,
        from: 130,
        durationInFrames: 50,
        content: '今天的节目',
        src: '',
        styles: {
          fontSize: 40,
          fontWeight: 'bold',
          color: 'rgba(255, 169, 2, 1)',
          backgroundColor: '',
          fontFamily: 'font-league-spartan',
          fontStyle: 'normal',
          textAlign: 'center',
          letterSpacing: 0,
          opacity: 1,
          zIndex: 1,
          transform: 'none'
        }
      }
    ]
  },

  {
    type: TrackType.TEXT,
    isGlobalTrack: true,
    overlays: [
      {
        ...DEFAULT_OVERLAY,
        id: 1784981,
        from: 0,
        durationInFrames: 30,
        type: OverlayType.TEXT,
        content: 'niodcnsioadcnio',
        src: '',
        styles: {
          fontSize: 40,
          fontWeight: 'bold',
          color: 'rgba(255, 169, 2, 1)',
          backgroundColor: '',
          fontFamily: 'font-league-spartan',
          fontStyle: 'normal',
          textAlign: 'center',
          letterSpacing: 0,
          opacity: 1,
          zIndex: 1,
          transform: 'none'
        }
      }
    ]
  }

  /*  {
     type: TrackType.VIDEO,
     isGlobalTrack: true,
     overlays: [
       {
         left: 0,
         top: 0,
         width: 1440,
         height: 1920,
         durationInFrames: 150,
         from: 0,
         id: 791325,
         rotation: 0,
                  isDragging: false,
         type: OverlayType.VIDEO,
         content:
           'https://images.pexels.com/videos/2821900/free-video-2821900.jpg?auto=compress&cs=tinysrgb&fit=crop&h=630&w=1200',
         src: 'https://videos.pexels.com/video-files/2821900/2821900-hd_1280_720_25fps.mp4',
         videoStartTime: 0,
         styles: {
           opacity: 1,
           zIndex: 100,
           transform: 'none',
           objectFit: 'cover',
           padding: '50px',
           paddingBackgroundColor: '#ffffff',
           filter:
             '',
         },
       },
     ]
   },
   {
     type: TrackType.IMAGE,
     isGlobalTrack: true,
     overlays: []
   },
   {
     type: TrackType.MIXED,
     isGlobalTrack: true,
     overlays: []
   }*/
]
