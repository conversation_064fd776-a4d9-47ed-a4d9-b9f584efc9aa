import { useMutation, useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '../../../constants/queryKeys'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { ResourceModule } from '@/libs/request/api/resource'

interface UpdateInteractInfoParams {
  resourceType: ResourceType
  resourceId: string | number
  collected: boolean
}

interface CacheUpdateContext {
  resourceType: ResourceType
  resourceId: string | number
  previousDataSnapshots: Record<string, any>
  previousCollectedData: unknown
}

function getResourceQueryKeys(resourceType: ResourceType): string[] {
  switch (resourceType) {
    case ResourceType.STICKER:
      return [QUERY_KEYS.PASTER_LIST]
    case ResourceType.MUSIC:
      return [QUERY_KEYS.MUSIC_LIST, QUERY_KEYS.MUSIC_RANK]
    case ResourceType.SOUND:
      return [QUERY_KEYS.VOICE_LIST]
    case ResourceType.FONT:
      return [QUERY_KEYS.FONT_LIST]
    default:
      return [QUERY_KEYS.RESOURCE_LIST]
  }
}

/**
 * 根据资源类型获取对应的收藏查询键
 */
function getResourceCollectedQueryKey(resourceType: ResourceType): string {
  switch (resourceType) {
    case ResourceType.STICKER:
      return QUERY_KEYS.PASTER_COLLECTED
    case ResourceType.MUSIC:
      return QUERY_KEYS.MUSIC_COLLECTED
    case ResourceType.SOUND:
      return QUERY_KEYS.VOICE_COLLECTED
    case ResourceType.FONT:
      return QUERY_KEYS.FONT_COLLECTED
    default:
      return QUERY_KEYS.RESOURCE_COLLECTED
  }
}

/**
 * 更新资源列表中指定资源的收藏状态
 */
function updateResourceCollectionStatus(oldData: any, resourceId: string | number, collected: boolean) {
  if (!oldData?.pages) return oldData

  return {
    ...oldData,
    pages: oldData.pages.map((page: any) => ({
      ...page,
      list: page.list.map((item: any) => {
        if (item.id === resourceId) {
          return {
            ...item,
            interactInfo: {
              ...(item.interactInfo || {}),
              collected
            }
          }
        }
        return item
      })
    }))
  }
}

/**
 * 从收藏列表中移除指定资源
 */
function removeResourceFromCollectedList(oldData: any, resourceId: string | number) {
  if (!oldData?.pages) return oldData

  return {
    ...oldData,
    pages: oldData.pages.map((page: any) => ({
      ...page,
      list: page.list.filter((item: any) => item.id !== resourceId)
    }))
  }
}

/**
 * 取消正在进行的查询
 */
async function cancelRelatedQueries(
  queryClient: any,
  queryKeys: string[],
  collectedQueryKey: string
) {
  // 取消所有相关的资源查询
  for (const queryKey of queryKeys) {
    await queryClient.cancelQueries({ queryKey: [queryKey] })
  }
  // 取消收藏查询
  await queryClient.cancelQueries({ queryKey: [collectedQueryKey] })
}

/**
 * 保存缓存数据快照
 */
function saveCacheSnapshots(queryClient: any, queryKeys: string[], collectedQueryKey: string) {
  const snapshots: Record<string, any> = {}

  // 保存所有相关查询的数据快照
  for (const queryKey of queryKeys) {
    snapshots[queryKey] = queryClient.getQueryData([queryKey])
  }

  return {
    previousDataSnapshots: snapshots,
    previousCollectedData: queryClient.getQueryData([collectedQueryKey])
  }
}

/**
 * 更新普通资源列表缓存
 */
function updateNormalListCache(
  queryClient: any,
  queryKeys: string[],
  resourceId: string | number,
  collected: boolean
) {
  // 更新所有相关的查询缓存
  for (const queryKey of queryKeys) {
    queryClient.setQueriesData(
      { queryKey: [queryKey] },
      (oldData: any) => updateResourceCollectionStatus(oldData, resourceId, collected)
    )
  }
}

/**
 * 更新收藏列表缓存
 */
function updateCollectedListCache(
  queryClient: any,
  collectedQueryKey: string,
  resourceId: string | number,
  collected: boolean
) {
  if (!collected) {
    // 取消收藏：从收藏列表中移除该项
    queryClient.setQueriesData(
      { queryKey: [collectedQueryKey] },
      (oldData: any) => removeResourceFromCollectedList(oldData, resourceId)
    )
  } else {
    // 收藏：让收藏列表重新获取数据
    queryClient.invalidateQueries({ queryKey: [collectedQueryKey] })
  }
}

/**
 * 恢复缓存数据快照
 */
function restoreCacheSnapshots(
  queryClient: any,
  queryKeys: string[],
  collectedQueryKey: string,
  context: CacheUpdateContext
) {
  // 恢复所有相关查询的数据
  for (const queryKey of queryKeys) {
    const previousData = context.previousDataSnapshots[queryKey]
    if (previousData) {
      queryClient.setQueryData([queryKey], previousData)
    }
  }

  // 恢复收藏列表
  if (context.previousCollectedData) {
    queryClient.setQueryData([collectedQueryKey], context.previousCollectedData)
  }
}

/**
 * 更新资源收藏状态的 Hook
 * 解耦版本：使用独立的辅助函数处理缓存更新逻辑
 */
export const useToggleResourceCollection = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ resourceId, collected }: UpdateInteractInfoParams) => {
      if (collected) {
        return ResourceModule.collect(resourceId.toString())
      } else {
        return ResourceModule.cancelCollect(resourceId.toString())
      }
    },

    // 乐观更新：立即更新缓存中的收藏状态
    onMutate: async ({ resourceType, resourceId, collected }) => {
      const queryKeys = getResourceQueryKeys(resourceType)
      const collectedQueryKey = getResourceCollectedQueryKey(resourceType)

      // 1. 取消正在进行的查询，避免竞态条件
      await cancelRelatedQueries(queryClient, queryKeys, collectedQueryKey)

      // 2. 保存当前数据快照，用于错误回滚
      const snapshots = saveCacheSnapshots(queryClient, queryKeys, collectedQueryKey)

      // 3. 更新普通资源列表缓存
      updateNormalListCache(queryClient, queryKeys, resourceId, collected)

      // 4. 更新收藏列表缓存
      updateCollectedListCache(queryClient, collectedQueryKey, resourceId, collected)

      // 5. 返回上下文信息用于错误回滚
      return {
        resourceType,
        resourceId,
        ...snapshots
      } as CacheUpdateContext
    },

    // 错误时回滚状态
    onError: (err, { resourceType }, context) => {
      console.error('更新收藏状态失败:', err)
      if (context) {
        const queryKeys = getResourceQueryKeys(resourceType)
        const collectedQueryKey = getResourceCollectedQueryKey(resourceType)

        // 恢复缓存数据快照
        restoreCacheSnapshots(queryClient, queryKeys, collectedQueryKey, context)
      }
    }

  })
}
