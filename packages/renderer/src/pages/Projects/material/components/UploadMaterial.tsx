import React, { useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { UploadIcon, FolderUpIcon, ChevronDown } from 'lucide-react'
import { FileUploader, FileUploaderRenderProps, UploadedFile } from '@/components/ui/file-uploader'
import { FolderUploader, FolderUploadedFile } from '@/components/ui/folder-uploader'
import { ResourceSource } from '@/types/resources'

interface UploadMaterialProps {
  folderUuid: string
  resourceType?: ResourceSource
  fileUploadTypes?: string[]
  onUpload?: (uploaded: (FolderUploadedFile | UploadedFile)[]) => void
  buttonLabel?: string
  customFileUploaderRender?: (props: FileUploaderRenderProps) => React.ReactNode
}

export const handleFileChange = (
  files: any[],
  setIsUploading: React.Dispatch<React.SetStateAction<boolean>>,
  setUploadProgress: React.Dispatch<React.SetStateAction<number>>,
) => {
  if (files.length > 0) {
    const uploadingFiles = files.filter(f => f.status === 'uploading')
    setIsUploading(uploadingFiles.length > 0)

    const totalProgress = files.reduce((sum, f) => sum + (f.progress || 0), 0)
    const avgProgress = totalProgress / files.length
    setUploadProgress(Math.round(avgProgress * 100))
  } else {
    setIsUploading(false)
    setUploadProgress(0)
  }
}

const UploadMaterial: React.FC<UploadMaterialProps> = ({
  folderUuid,
  resourceType = ResourceSource.MEDIA,
  fileUploadTypes,
  onUpload,
  buttonLabel = '上传',
  customFileUploaderRender,
}) => {
  const DefaultFileUploader = ({ getRootProps, getInputProps, isLoading }: FileUploaderRenderProps) => (
    <div className="flex items-center justify-center gap-2">
      <UploadIcon className="w-3.5 h-3.5" />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '上传文件'}
      </div>
    </div>
  )
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [isUploading, setIsUploading] = useState<boolean>(false)

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="w-36 border-0 bg-primary/10 flex justify-between gap-1">
          <span>
            {buttonLabel}
            {isUploading && <span>({`上传进度：${uploadProgress}%`})</span>}
          </span>
          <ChevronDown className="w-3.5 h-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-36 p-0">
        <div className="py-1">
          {/* 上传文件 */}
          <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
            <FileUploader
              maxSize={1.5 * 1024 * 1024 * 1024}
              folderUuid={folderUuid}
              {...(fileUploadTypes ? { fileTypes: fileUploadTypes } : {})}
              renderCustomComponent={customFileUploaderRender || DefaultFileUploader}
              onChange={files => {
                handleFileChange(files, setIsUploading, setUploadProgress)
              }}
              onUpload={file => {
                // 上传完成后可隐藏进度（可选延时让用户看到100%）
                setTimeout(() => setIsUploading(false), 1000)
                onUpload?.(file)
              }}
            />
          </button>

          {/* 上传文件夹 */}
          <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
            <FolderUploader
              maxSize={1.5 * 1024 * 1024 * 1024}
              resourceType={resourceType}
              {...(fileUploadTypes ? { fileTypes: fileUploadTypes } : {})}
              folderUuid={folderUuid}
              children={
                <div className="flex items-center justify-center">
                  <FolderUpIcon className="w-3.5 h-3.5 mr-2" />
                  上传文件夹
                </div>
              }
              isShowUploadedFiles={false}
              showFileList={false}
              onProgress={(current, total) => {
                if (current === 1) setIsUploading(true) // 第一个文件开始时才显示
                if (current === total) {
                  setTimeout(() => setIsUploading(false), 1000) // 全部完成后隐藏
                }
              }}
              onUpload={onUpload}
            />
          </button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
export default UploadMaterial
