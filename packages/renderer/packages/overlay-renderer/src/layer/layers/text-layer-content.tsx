import React from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { CloudTextRenderer } from './cloud-layer-text-renderer'
import { useOverlayAnimation } from '../../hooks'

interface TextLayerContentProps {
  overlay: TextOverlay
}

export const TextLayerContent: React.FC<TextLayerContentProps> = ({ overlay }) => {
  const animation = useOverlayAnimation(overlay)

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    textAlign: overlay.styles.textAlign,
    justifyContent:
      overlay.styles.textAlign === 'center'
        ? 'center'
        : overlay.styles.textAlign === 'right'
          ? 'flex-end'
          : 'flex-start',
    overflow: 'hidden',
    boxSizing: 'border-box',
    ...animation,
  }

  return (
    <CloudTextRenderer
      overlay={overlay}
      containerStyle={containerStyle}
    />
  )
}
