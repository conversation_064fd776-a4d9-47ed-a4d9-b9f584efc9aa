import { Injectable, Inject } from '@nestjs/common'
import { Task } from '@app/shared/types/database.types.js'
import { TaskModel } from '@/infra/models/TaskModel.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'

/**
 * 任务仓储类
 */
@Injectable()
export class TaskRepository extends BaseRepository<TaskModel, Task.CreateParams, Task.UpdateParams, Task.QueryParams> {

  protected toModel(data: Record<string, any> | null): TaskModel | null {
    throw new Error('Method not implemented.')
  }

  /**
   * 表名
   */
  protected readonly tableName = 'task'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'name', 'status', 'progress', 'duration', 'size',
    'updated_at', 'created_at',
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'team_id', 'group_id', 'uid', 'status',
    'folder_id', 'type', 'deleted_at', 'tag_id', 'platform',
  ]

  /**
   * 构造函数
   * @param dbService 数据库服务实例
   */
  constructor(
    @Inject(NestDatabaseService)
    dbService: NestDatabaseService
  ) {
    super(dbService)
  }

  /**
   * 查找用户任务
   * @param uid 用户ID
   * @param status 状态过滤
   * @param teamId 团队ID
   * @returns 任务列表
   */
  findUserTasks(uid: string, status?: number, teamId?: number | null): TaskModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
    `

    const params: any[] = [uid]

    if (status !== undefined) {
      sql += ' AND status = ?'
      params.push(status)
    }

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 根据文件夹查找任务
   * @param folderId 文件夹ID
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 任务列表
   */
  findByFolder(folderId: string, uid: string, teamId?: number | null): TaskModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE folder_id = ? AND uid = ? AND deleted_at = 0
    `

    const params: any[] = [folderId, uid]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 根据类型查找任务
   * @param uid 用户ID
   * @param type 任务类型
   * @param teamId 团队ID
   * @returns 任务列表
   */
  findByType(uid: string, type: number, teamId?: number | null): TaskModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND type = ? AND deleted_at = 0
    `

    const params: any[] = [uid, type]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 搜索任务
   * @param keyword 关键词
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 任务列表
   */
  search(keyword: string, uid: string, teamId?: number | null): TaskModel[] {
    const searchKeyword = `%${keyword}%`

    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
      AND (name LIKE ? OR resource_id LIKE ? OR task_nos LIKE ?)
    `

    const params: any[] = [uid, searchKeyword, searchKeyword, searchKeyword]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 更新任务状态
   * @param id 任务ID
   * @param reason 原因
   * @param status 状态
   * @param extra 额外参数
   * @returns 是否更新成功
   */
  updateStatus(id: number, reason: string, status: number, extra: any): boolean {
    const stmt = this.db.prepare(`
      UPDATE ${this.tableName}
      SET status = ?, updated_at = ?
      WHERE id = ?
    `)

    const result = stmt.run(status, Date.now(), id)
    return result.changes > 0
  }

  /**
   * 批量移动任务到指定文件夹
   * @param ids 任务ID数组
   * @param folderId 目标文件夹ID
   * @returns 移动的记录数
   */
  batchMove(ids: string[], folderId: string): number {
    const placeholders = ids.map(() => '?').join(',')
    const stmt = this.db.prepare(`
      UPDATE ${this.tableName}
      SET folder_id = ?, updated_at = ?
      WHERE id IN (${placeholders})
    `)

    const result = stmt.run(folderId, Date.now(), ...ids)
    return result.changes
  }

  /**
   * 获取任务统计数据
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 统计数据
   */
  getTaskStats(uid: string, teamId?: number | null): any {
    let sql = `
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as processing,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as failed
      FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
    `

    const params: any[] = [uid]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    const stmt = this.db.prepare(sql)
    return stmt.get(...params)
  }
}
