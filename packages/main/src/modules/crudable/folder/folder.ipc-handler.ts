import { Inject, Injectable } from '@nestjs/common'
import { CrudableBaseIPCHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { FolderService } from './folder.service.js'

/**
 * 文件夹IPC处理器
 * 负责处理渲染进程与主进程之间的文件夹相关通信
 */
@Injectable()
export class Folder<PERSON>CHandler extends CrudableBaseIPCHandler<'folder'> {

  /**
   * 构造函数
   */
  constructor(
    @Inject(FolderService)
    readonly folderService: FolderService
  ) {
    super(folderService, 'folder')
  }

  /**
   * 注册额外的IPC处理程序
   */
  protected registerExtraHandlers(): void {
    // 获取子文件夹
    this.registerHandler(
      'children',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取子文件夹参数不能为空')
        }
        if (data.parentId === undefined || data.parentId === null) {
          throw new IPCHandlerError('父文件夹ID不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.folderService.getChildFolders(data.parentId, data.uid, data.teamId)
      },
    )

    // 获取文件夹路径
    this.registerHandler(
      'path',
      async id => {
        if (id === undefined || id === null) {
          throw new IPCHandlerError('文件夹ID不能为空')
        }
        return this.folderService.getFolderPath(id)
      },
    )

    // 创建默认文件夹结构
    this.registerHandler(
      'createDefaultStructure',
      async data => {
        if (!data) {
          throw new IPCHandlerError('创建默认文件夹结构参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.folderService.createDefaultFolderStructure(data.uid, data.teamId)
      },
    )
  }
}
