import { Injectable, Inject } from '@nestjs/common'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'
import { FolderModel } from '@/infra/models/FolderModel.js'
import { Folder } from '@app/shared/types/database.types.js'
import { FolderRepository } from './folder.repository.js'

/**
 * 文件夹服务
 * 负责处理文件夹相关的业务逻辑
 */
@Injectable()
export class FolderService extends CrudableBaseService<
  FolderModel,
  Folder.CreateParams,
  Folder.UpdateParams,
  Folder.QueryParams,
  FolderRepository
> {

  /**
   * 构造函数
   * @param repository 文件夹仓储实例
   */
  constructor(
    @Inject(FolderRepository)
    repository: FolderRepository
  ) {
    super(repository)
  }

  /**
   * 获取子文件夹
   * @param parentId 父文件夹ID
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 子文件夹列表
   */
  getChildFolders(parentId: number, uid: string, teamId?: number | null): FolderModel[] {
    return this.repository.findChildren(parentId, uid, teamId)
  }

  /**
   * 获取文件夹路径
   * @param id 文件夹ID
   * @returns 文件夹路径数组
   */
  getFolderPath(id: number): FolderModel[] {
    return this.repository.getFolderPath(id)
  }

  /**
   * 创建默认文件夹结构
   * 为新用户创建默认的文件夹结构
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 创建的根文件夹
   */
  async createDefaultFolderStructure(uid: string, teamId?: number | null): Promise<FolderModel> {
    // 创建根文件夹
    const rootFolder = await this.create({
      title: '我的文件夹',
      uid,
      team_id: teamId,
      parent_id: 0,
      auto_create: 2,
    })

    // 创建子文件夹
    const defaultFolders = [
      { title: '视频素材', auto_create: 2 },
      { title: '音频素材', auto_create: 2 },
      { title: '图片素材', auto_create: 2 },
      { title: '我的项目', auto_create: 2 },
    ]

    for (const folder of defaultFolders) {
      await this.create({
        title: folder.title,
        uid,
        team_id: teamId,
        parent_id: rootFolder.id,
        auto_create: folder.auto_create,
      })
    }

    return rootFolder
  }
}
