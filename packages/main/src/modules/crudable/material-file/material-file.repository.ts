import { Injectable, Inject } from '@nestjs/common'
import { MaterialFile } from '@app/shared/types/database.types.js'
import { MaterialFileModel } from '@/infra/models/MaterialFileModel.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'

/**
 * 素材文件仓储类
 */
@Injectable()
export class MaterialFileRepository extends BaseRepository<MaterialFileModel, MaterialFile.CreateParams, MaterialFile.UpdateParams, MaterialFile.QueryParams> {

  /**
   * 表名
   */
  protected readonly tableName = 'material_file'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'parent_id', 'material_type', 'title', 'size',
    'duration', 'updated_at', 'created_at',
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'parent_id', 'team_id', 'uid', 'material_type',
    'status', 'clip_cloud_or_local', 'deleted_at', 'tag_id',
  ]

  /**
   * 构造函数
   * @param dbService 数据库服务实例
   */
  constructor(
    @Inject(NestDatabaseService)
    dbService: NestDatabaseService
  ) {
    super(dbService)
  }

  /**
   * 根据哈希查找素材
   * @param hash 文件哈希
   * @returns 素材文件记录或null
   */
  findByHash(hash: string): MaterialFileModel | null {
    const stmt = this.db.prepare(`
      SELECT * FROM ${this.tableName}
      WHERE file_hash = ? AND deleted_at = 0
      LIMIT 1
    `)
    const data = stmt.get(hash) as Record<string, any> | undefined
    return data ? this.toModel(data) : null
  }

  /**
   * 查找用户的素材文件
   * @param uid 用户ID
   * @param folderId 文件夹ID
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  findUserMaterials(uid: string, folderId?: number, teamId?: number | null): MaterialFileModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
    `

    const params: any[] = [uid]

    if (folderId !== undefined) {
      sql += ' AND parent_id = ?'
      params.push(folderId)
    }

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 根据类型查找素材
   * @param uid 用户ID
   * @param materialType 素材类型
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  findByType(uid: string, materialType: number, teamId?: number | null): MaterialFileModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND material_type = ? AND deleted_at = 0
    `

    const params: any[] = [uid, materialType]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 搜索素材文件
   * @param keyword 关键词
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  search(keyword: string, uid: string, teamId?: number | null): MaterialFileModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
      AND (title LIKE ? OR path LIKE ? OR source_path LIKE ?)
    `

    const searchPattern = `%${keyword}%`
    const params: any[] = [uid, searchPattern, searchPattern, searchPattern]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at DESC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 更新素材状态
   * @param id 素材ID
   * @param reason 原因
   * @param status 状态
   * @returns 是否更新成功
   */
  updateStatus(id: string, reason: string, status: number): boolean {
    const stmt = this.db.prepare(`
      UPDATE ${this.tableName}
      SET status = ?, updated_at = ?
      WHERE id = ?
    `)

    const result = stmt.run(status, Date.now(), id)
    return result.changes > 0
  }

  /**
   * 批量移动素材到指定文件夹
   * @param ids 素材ID数组
   * @param folderId 目标文件夹ID
   * @returns 移动的记录数
   */
  batchMove(ids: string[], folderId: number): number {
    const placeholders = ids.map(() => '?').join(',')
    const stmt = this.db.prepare(`
      UPDATE ${this.tableName}
      SET parent_id = ?, updated_at = ?
      WHERE id IN (${placeholders})
    `)

    const result = stmt.run(folderId, Date.now(), ...ids)
    return result.changes
  }

  protected toModel(data: Record<string, any> | null): MaterialFileModel | null {
    throw new Error('Not implemented yet')
  }
}
