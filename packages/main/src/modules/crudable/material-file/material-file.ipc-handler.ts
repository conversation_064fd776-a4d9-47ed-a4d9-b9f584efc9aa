import { Inject, Injectable } from '@nestjs/common'
import { CrudableBaseIPCHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { MaterialFileService } from './material-file.service.js'

/**
 * 素材文件IPC处理器
 * 负责处理渲染进程与主进程之间的素材文件相关通信
 */
@Injectable()
export class MaterialFileIPCHandler extends CrudableBaseIPCHandler<'materialFile'> {

  /**
   * 构造函数
   */
  constructor(
    @Inject(MaterialFileService)
    readonly materialFileService: MaterialFileService
  ) {
    super(materialFileService, 'materialFile')
  }

  /**
   * 注册额外的IPC处理程序
   */
  protected registerExtraHandlers(): void {
    // 根据哈希查找素材
    this.registerHandler(
      'findByHash',
      async hash => {
        if (!hash) {
          throw new IPCHandlerError('文件哈希不能为空')
        }
        return this.materialFileService.findByHash(hash)
      },
    )

    // 查找用户素材
    this.registerHandler(
      'userMaterials',
      async data => {
        if (!data) {
          throw new IPCHandlerError('查找用户素材参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.materialFileService.getUserMaterials(data.uid, data.folderId, data.teamId)
      },
    )

    // 根据类型查找素材
    this.registerHandler(
      'byType',
      async data => {
        if (!data) {
          throw new IPCHandlerError('根据类型查找素材参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        if (data.materialType === undefined || data.materialType === null) {
          throw new IPCHandlerError('素材类型不能为空')
        }
        return this.materialFileService.getMaterialsByType(data.uid, data.materialType, data.teamId)
      },
    )

    // 搜索素材
    this.registerHandler(
      'search',
      async data => {
        if (!data) {
          throw new IPCHandlerError('搜索素材参数不能为空')
        }
        if (!data.keyword) {
          throw new IPCHandlerError('搜索关键词不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.materialFileService.searchMaterials(data.keyword, data.uid, data.teamId)
      },
    )

    // 更新素材状态
    this.registerHandler(
      'updateStatus',
      async data => {
        if (!data) {
          throw new IPCHandlerError('更新素材状态参数不能为空')
        }
        if (!data.id) {
          throw new IPCHandlerError('素材ID不能为空')
        }
        if (data.status === undefined || data.status === null) {
          throw new IPCHandlerError('素材状态不能为空')
        }
        return this.materialFileService.updateStatus(data.id, data.status, data.reason)
      },
    )

    // 批量移动素材
    this.registerHandler(
      'batchMove',
      async data => {
        if (!data) {
          throw new IPCHandlerError('批量移动素材参数不能为空')
        }
        if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
          throw new IPCHandlerError('素材ID列表不能为空')
        }
        if (data.folderId === undefined || data.folderId === null) {
          throw new IPCHandlerError('目标文件夹ID不能为空')
        }
        return this.materialFileService.batchMoveMaterials(data.ids, data.folderId)
      },
    )
  }
}
