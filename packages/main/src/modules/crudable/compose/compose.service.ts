import { Compose } from '@app/shared/types/database.types.js'
import { ComposeModel } from '@/infra/models/ComposeModel.js'
import { Injectable } from '@nestjs/common'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'
import { ComposeRepository } from './compose.repository.js'

/**
 * 合成记录服务类
 */
@Injectable()
export class ComposeService extends CrudableBaseService<
  ComposeModel,
  Compose.CreateParams,
  Compose.UpdateParams,
  Compose.QueryParams,
  ComposeRepository
> {

  constructor(protected repository: ComposeRepository) {
    super(repository)
  }

  /**
   * 获取团队合成记录
   * @param teamId 团队ID
   * @param status 状态过滤
   * @returns 合成记录列表
   */
  getTeamComposes(teamId: number, status?: number): ComposeModel[] {
    try {
      return this.repository.findTeamComposes(teamId, status)
    } catch (error: any) {
      throw new Error(`获取团队合成记录失败: ${error.message}`)
    }
  }

  /**
   * 获取脚本相关的合成记录
   * @param scriptId 脚本ID
   * @param teamId 团队ID
   * @returns 合成记录列表
   */
  getComposesByScriptId(scriptId: string, teamId?: number): ComposeModel[] {
    try {
      return this.repository.findByScriptId(scriptId, teamId)
    } catch (error: any) {
      throw new Error(`获取脚本合成记录失败: ${error.message}`)
    }
  }

  /**
   * 搜索合成记录
   * @param keyword 关键词
   * @param teamId 团队ID
   * @returns 合成记录列表
   */
  searchComposes(keyword: string, teamId?: number): ComposeModel[] {
    try {
      return this.repository.search(keyword, teamId)
    } catch (error: any) {
      throw new Error(`搜索合成记录失败: ${error.message}`)
    }
  }

  /**
   * 更新下载状态
   * @param id 合成记录ID
   * @param downloadStatus 下载状态
   * @param progress 下载进度
   * @param reason 失败原因
   * @returns 是否更新成功
   */
  updateDownloadStatus(id: number, downloadStatus: number, progress?: number, reason?: string): boolean {
    try {
      return this.repository.updateDownloadStatus(id, downloadStatus, progress, reason)
    } catch (error: any) {
      throw new Error(`更新下载状态失败: ${error.message}`)
    }
  }

  /**
   * 标记为已读
   * @param id 合成记录ID
   * @returns 是否更新成功
   */
  markAsRead(id: number): boolean {
    try {
      return this.repository.markAsRead(id)
    } catch (error: any) {
      throw new Error(`标记已读失败: ${error.message}`)
    }
  }

  /**
   * 获取合成记录统计数据
   * @param teamId 团队ID
   * @returns 统计结果
   */
  getComposeStats(teamId?: number): Compose.StatsResult {
    try {
      return this.repository.getStats(teamId)
    } catch (error: any) {
      throw new Error(`获取合成记录统计数据失败: ${error.message}`)
    }
  }
}
