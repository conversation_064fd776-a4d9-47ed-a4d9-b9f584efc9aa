import { Inject, Injectable } from '@nestjs/common'
import { CrudableBaseIPCHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { ComposeService } from './compose.service.js'

@Injectable()
export class ComposeIPCHandler extends CrudableBaseIPCHandler<'compose'> {

  /**
   * 构造函数
   */
  constructor(
    @Inject(ComposeService)
    readonly composeService: ComposeService
  ) {
    super(composeService, 'compose')
  }

  /**
   * 注册额外的IPC处理程序
   */
  protected registerExtraHandlers(): void {
    // 获取团队合成记录
    this.registerHandler(
      'teamComposes',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取团队合成记录参数不能为空')
        }
        if (!data.teamId) {
          throw new IPCHandlerError('团队ID不能为空')
        }
        return this.composeService.getTeamComposes(data.teamId, data.status)
      },
    )

    // 获取脚本相关的合成记录
    this.registerHandler(
      'scriptComposes',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取脚本合成记录参数不能为空')
        }
        if (!data.scriptId) {
          throw new IPCHandlerError('脚本ID不能为空')
        }
        return this.composeService.getComposesByScriptId(data.scriptId, data.teamId)
      },
    )

    // 搜索合成记录
    this.registerHandler(
      'searchCompose',
      async data => {
        if (!data) {
          throw new IPCHandlerError('搜索合成记录参数不能为空')
        }
        if (!data.keyword) {
          throw new IPCHandlerError('搜索关键词不能为空')
        }
        return this.composeService.searchComposes(data.keyword, data.teamId)
      },
    )

    // 更新下载状态
    this.registerHandler(
      'updateDownloadStatus',
      async data => {
        if (!data) {
          throw new IPCHandlerError('更新下载状态参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('合成记录ID不能为空')
        }
        if (data.downloadStatus === undefined || data.downloadStatus === null) {
          throw new IPCHandlerError('下载状态不能为空')
        }
        return this.composeService.updateDownloadStatus(data.id, data.downloadStatus, data.progress, data.reason)
      },
    )

    // 标记为已读
    this.registerHandler(
      'markAsRead',
      async id => {
        if (id === undefined || id === null) {
          throw new IPCHandlerError('合成记录ID不能为空')
        }
        return this.composeService.markAsRead(id)
      },
    )

    // 获取合成记录统计数据
    this.registerHandler(
      'getComposeStats',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取合成记录统计数据参数不能为空')
        }
        return this.composeService.getComposeStats(data.teamId)
      },
    )
  }
}

