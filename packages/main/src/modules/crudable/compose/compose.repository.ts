import { Compose } from '@app/shared/types/database.types.js'
import { ComposeModel } from '@/infra/models/ComposeModel.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { Inject, Injectable } from '@nestjs/common'
import { NestDatabaseService } from '@/modules/global/database.service.js'

/**
 * 合成记录仓储类
 */
@Injectable()
export class ComposeRepository extends BaseRepository<ComposeModel, Compose.CreateParams, Compose.UpdateParams, Compose.QueryParams> {

  /**
   * 表名
   */
  protected readonly tableName = 'compose'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'cid', 'name', 'status', 'download_status', 'duration', 'size',
    'download_count', 'download_progress', 'updated_at', 'created_at', 'download_at'
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'cid', 'team_id', 'script_id', 'status', 'download_status',
    'read', 'notify', 'auto_download', 'deleted_at'
  ]

  /**
   * 构造函数
   * @param dbService 数据库服务实例
   */
  constructor(
    @Inject(NestDatabaseService)
    readonly dbService: NestDatabaseService
  ) {
    super(dbService)
  }

  /**
   * 将数据库记录转换为领域模型
   * @param data 数据库记录
   * @returns 合成记录领域模型
   */
  protected toModel(data: Record<string, any> | null): ComposeModel | null {
    if (!data) return null
    return ComposeModel.fromRow(data)
  }

  /**
   * 获取团队合成记录
   * @param teamId 团队ID
   * @param status 状态过滤
   * @returns 合成记录列表
   */
  findTeamComposes(teamId: number, status?: number): ComposeModel[] {
    try {
      let sql = `
        SELECT *
        FROM ${this.tableName}
        WHERE team_id = ?
          AND deleted_at = 0
      `

      const params: any[] = [teamId]

      if (status !== undefined) {
        sql += ' AND status = ?'
        params.push(status)
      }

      sql += ' ORDER BY created_at DESC'

      const stmt = this.db.prepare(sql)
      const data = stmt.all(...params) as Record<string, any>[]
      return this.toModelArray(data)
    } catch (error: any) {
      throw new Error(`获取团队合成记录失败: ${error.message}`)
    }
  }

  /**
   * 获取脚本相关的合成记录
   * @param scriptId 脚本ID
   * @param teamId 团队ID
   * @returns 合成记录列表
   */
  findByScriptId(scriptId: string, teamId?: number): ComposeModel[] {
    try {
      let sql = `
        SELECT *
        FROM ${this.tableName}
        WHERE script_id = ?
          AND deleted_at = 0
      `

      const params: any[] = [scriptId]

      if (teamId !== undefined) {
        sql += ' AND team_id = ?'
        params.push(teamId)
      }

      sql += ' ORDER BY created_at DESC'

      const stmt = this.db.prepare(sql)
      const data = stmt.all(...params) as Record<string, any>[]
      return this.toModelArray(data)
    } catch (error: any) {
      throw new Error(`获取脚本合成记录失败: ${error.message}`)
    }
  }

  /**
   * 搜索合成记录
   * @param keyword 关键词
   * @param teamId 团队ID
   * @returns 合成记录列表
   */
  search(keyword: string, teamId?: number): ComposeModel[] {
    try {
      if (!keyword) {
        return []
      }

      let sql = `
        SELECT *
        FROM ${this.tableName}
        WHERE deleted_at = 0
          AND (
          name LIKE ? OR script_id LIKE ?
          )
      `

      const params: any[] = [`%${keyword}%`, `%${keyword}%`]

      if (teamId !== undefined) {
        sql += ' AND team_id = ?'
        params.push(teamId)
      }

      sql += ' ORDER BY created_at DESC'

      const stmt = this.db.prepare(sql)
      const data = stmt.all(...params) as Record<string, any>[]
      return this.toModelArray(data)
    } catch (error: any) {
      throw new Error(`搜索合成记录失败: ${error.message}`)
    }
  }

  /**
   * 更新下载状态
   * @param id 合成记录ID
   * @param downloadStatus 下载状态
   * @param progress 下载进度
   * @param reason 失败原因
   * @returns 是否更新成功
   */
  updateDownloadStatus(id: number, downloadStatus: number, progress?: number, reason?: string): boolean {
    try {
      const params: Compose.UpdateParams = {
        download_status: downloadStatus,
        updated_at: Math.floor(Date.now() / 1000)
      }

      if (progress !== undefined) {
        params.download_progress = progress
      }

      if (reason !== undefined) {
        params.download_reason = reason
      }

      if (downloadStatus === Compose.DownloadStatus.COMPLETED) {
        params.download_at = Math.floor(Date.now() / 1000)

        // 获取当前下载次数并加1
        const sql = `SELECT download_count
                     FROM ${this.tableName}
                     WHERE id = ?`
        const stmt = this.db.prepare(sql)
        const result = stmt.get(id) as Record<string, any> | undefined
        if (result) {
          params.download_count = (result.download_count || 0) + 1
        }
      }

      return this.update(id, params)
    } catch (error: any) {
      throw new Error(`更新下载状态失败: ${error.message}`)
    }
  }

  /**
   * 标记为已读
   * @param id 合成记录ID
   * @returns 是否更新成功
   */
  markAsRead(id: number): boolean {
    try {
      return this.update(id, { read: 1 })
    } catch (error: any) {
      throw new Error(`标记已读失败: ${error.message}`)
    }
  }

  /**
   * 获取合成记录统计数据
   * @param teamId 团队ID
   * @returns 统计结果
   */
  getStats(teamId?: number): Compose.StatsResult {
    try {
      const params: any[] = []
      let whereClause = 'deleted_at = 0'

      if (teamId !== undefined) {
        whereClause += ' AND team_id = ?'
        params.push(teamId)
      }

      // 总数
      let sql = `SELECT COUNT(*) as count
                 FROM ${this.tableName}
                 WHERE ${whereClause}`
      let stmt = this.db.prepare(sql)
      const totalResult = stmt.get(...params) as { count: number } | undefined
      const totalCount = totalResult?.count || 0

      // 完成数
      sql = `SELECT COUNT(*) as count
             FROM ${this.tableName}
             WHERE ${whereClause} AND status = ?`
      stmt = this.db.prepare(sql)
      const completedResult = stmt.get(...params, Compose.Status.COMPLETED) as { count: number } | undefined
      const completedCount = completedResult?.count || 0

      // 失败数
      stmt = this.db.prepare(sql)
      const failedResult = stmt.get(...params, Compose.Status.FAILED) as { count: number } | undefined
      const failedCount = failedResult?.count || 0

      // 等待数
      stmt = this.db.prepare(sql)
      const pendingResult = stmt.get(...params, Compose.Status.PENDING) as { count: number } | undefined
      const pendingCount = pendingResult?.count || 0

      // 处理中数量
      stmt = this.db.prepare(sql)
      const processingResult = stmt.get(...params, Compose.Status.PROCESSING) as { count: number } | undefined
      const processingCount = processingResult?.count || 0

      // 已下载数量
      sql = `SELECT COUNT(*) as count
             FROM ${this.tableName}
             WHERE ${whereClause} AND download_status = ?`
      stmt = this.db.prepare(sql)
      const downloadedResult = stmt.get(...params, Compose.DownloadStatus.COMPLETED) as { count: number } | undefined
      const downloadedCount = downloadedResult?.count || 0

      return {
        totalCount,
        completedCount,
        failedCount,
        pendingCount,
        processingCount,
        downloadedCount
      }
    } catch (error: any) {
      throw new Error(`获取统计数据失败: ${error.message}`)
    }
  }
}
