import type { AppInitConfig } from './AppInitConfig.js'

import { app } from 'electron'
import { NestFactory } from '@nestjs/core'

import 'dotenv/config'
import 'reflect-metadata'

import { RootModule } from './root.module.js'
import { createModuleRunner } from '@/ModuleRunner.js'
import { createWindowManagerModule } from '@/app-modules/WindowManager.js'
import { disallowMultipleAppInstance } from '@/app-modules/SingleInstanceApp.js'
import { terminateAppOnLastWindowClose } from '@/app-modules/ApplicationTerminatorOnLastWindowClose.js'
import { hardwareAccelerationMode } from '@/app-modules/HardwareAccelerationModule.js'
import { autoUpdater } from './app-modules/AutoUpdater.js'
import { allowInternalOrigins } from '@/app-modules/BlockNotAllowdOrigins.js'
import { allowExternalUrls } from '@/app-modules/ExternalUrls.js'

export async function initApp(initConfig: AppInitConfig) {
  await app.whenReady()

  // 创建 NestJS 应用上下文，这会触发所有模块的初始化
  const nestApp = await NestFactory.createApplicationContext(RootModule)
  await nestApp.init()

  const moduleRunner = createModuleRunner()
    .init(createWindowManagerModule({ initConfig, openDevTools: true }))
    .init(disallowMultipleAppInstance())
    .init(terminateAppOnLastWindowClose())
    .init(hardwareAccelerationMode({ enable: true }))
    .init(autoUpdater())
    // Install DevTools extension if needed
    // .init(chromeDevToolsExtension({extension: 'VUEJS3_DEVTOOLS'}))

    // Security
    .init(allowInternalOrigins(
      new Set(initConfig.renderer instanceof URL ? [initConfig.renderer.origin] : []),
    ))
    .init(allowExternalUrls(
      new Set(
        initConfig.renderer instanceof URL
          ? [
            'https://vite.dev',
            'https://developer.mozilla.org',
            'https://solidjs.com',
            'https://qwik.dev',
            'https://lit.dev',
            'https://react.dev',
            'https://preactjs.com',
            'https://www.typescriptlang.org',
            'https://vuejs.org',
          ]
          : [],
      )),
    )

  await moduleRunner
}
