import { Task } from '@app/shared/types/database.types.js'

/**
 * 任务领域模型类
 * 代表合成任务实体及其业务规则
 */
export class TaskModel implements Task.ITask {

  /**
     * 任务ID
     */
  id: number

  /**
     * 团队ID
     */
  team_id: number

  /**
     * 分组ID
     */
  group_id: number

  /**
     * 是否生成封面帧
     */
  make_cover_frame: number

  /**
     * 是否生成追踪帧
     */
  make_track_frame: number

  /**
     * 是否绑定素材
     */
  need_bind: number

  /**
     * 本地素材路径
     */
  path: string

  /**
     * 用户ID
     */
  uid: string

  /**
     * 云端URL
     */
  url: string

  /**
     * 脚本ID
     */
  script_id: number

  /**
     * 素材ID
     */
  resource_id: string

  /**
     * 封面图
     */
  cover: string

  /**
     * 云端封面帧路径
     */
  cover_frame: string

  /**
     * 云端追踪帧路径
     */
  track_frame: string

  /**
     * 任务名
     */
  name: string

  /**
     * 编解码器名称
     */
  codec_name: string

  /**
     * 哈希
     */
  hash: string

  /**
     * 时长（秒）
     */
  duration: number

  /**
     * 宽度
     */
  width: number

  /**
     * 高度
     */
  height: number

  /**
     * 文件大小
     */
  size: number

  /**
     * 状态
     */
  status: number

  /**
     * 进度（百分比）
     */
  progress: number

  /**
     * 所属文件夹
     */
  folder_id: string

  /**
     * 上传任务ID
     */
  upload_id: string

  /**
     * 类型
     */
  type: number

  /**
     * 失败原因
     */
  reason: string

  /**
     * 多任务编号
     */
  task_nos: string

  /**
     * 附加任务
     */
  extra_tasks: string

  /**
     * 上传类型
     */
  upload_type: string

  /**
     * 删除时间戳
     */
  deleted_at: number

  /**
     * 更新时间戳
     */
  updated_at: number

  /**
     * 创建时间戳
     */
  created_at: number

  /**
     * 标签ID
     */
  tag_id: number

  /**
     * 存储key
     */
  object_key: string

  /**
     * 平台
     */
  platform: number

  /**
     * 构造函数
     * @param data 任务数据
     */
  constructor(data: Partial<TaskModel> = {}) {
    this.id = data.id ?? 0
    this.team_id = data.team_id ?? 0
    this.group_id = data.group_id ?? 0
    this.make_cover_frame = data.make_cover_frame ?? 0
    this.make_track_frame = data.make_track_frame ?? 0
    this.need_bind = data.need_bind ?? 0
    this.path = data.path ?? ''
    this.uid = data.uid ?? ''
    this.url = data.url ?? ''
    this.script_id = data.script_id ?? 0
    this.resource_id = data.resource_id ?? ''
    this.cover = data.cover ?? ''
    this.cover_frame = data.cover_frame ?? ''
    this.track_frame = data.track_frame ?? ''
    this.name = data.name ?? ''
    this.codec_name = data.codec_name ?? ''
    this.hash = data.hash ?? ''
    this.duration = data.duration ?? 0
    this.width = data.width ?? 0
    this.height = data.height ?? 0
    this.size = data.size ?? 0
    this.status = data.status ?? 0
    this.progress = data.progress ?? 0
    this.folder_id = data.folder_id ?? ''
    this.upload_id = data.upload_id ?? ''
    this.type = data.type ?? 0
    this.reason = data.reason ?? ''
    this.task_nos = data.task_nos ?? ''
    this.extra_tasks = data.extra_tasks ?? ''
    this.upload_type = data.upload_type ?? ''
    this.deleted_at = data.deleted_at ?? 0
    this.updated_at = data.updated_at ?? Date.now()
    this.created_at = data.created_at ?? Date.now()
    this.tag_id = data.tag_id ?? 0
    this.object_key = data.object_key ?? ''
    this.platform = data.platform ?? Task.Platform.DESKTOP
  }

  /**
     * 判断是否已删除
     */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
     * 判断是否属于指定用户
     * @param uid 用户ID
     */
  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  /**
     * 判断是否属于指定团队
     * @param teamId 团队ID
     */
  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  /**
     * 判断任务是否处于等待状态
     */
  isPending(): boolean {
    return this.status === Task.Status.PENDING
  }

  /**
     * 判断任务是否处于处理中状态
     */
  isProcessing(): boolean {
    return this.status === Task.Status.PROCESSING
  }

  /**
     * 判断任务是否已完成
     */
  isCompleted(): boolean {
    return this.status === Task.Status.COMPLETED
  }

  /**
     * 判断任务是否失败
     */
  isFailed(): boolean {
    return this.status === Task.Status.FAILED
  }

  /**
     * 判断任务是否已取消
     */
  isCanceled(): boolean {
    return this.status === Task.Status.CANCELED
  }

  /**
     * 获取任务的显示名称
     */
  getDisplayName(): string {
    return this.name || '未命名任务'
  }

  /**
     * 获取任务进度百分比字符串
     */
  getProgressText(): string {
    return `${this.progress}%`
  }

  /**
     * 获取格式化的时长
     */
  getFormattedDuration(): string {
    const totalSeconds = this.duration
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = Math.floor(totalSeconds % 60)

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  /**
     * 获取格式化的文件大小
     */
  getFormattedSize(): string {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (this.size === 0) return '0 B'

    const i = Math.floor(Math.log(this.size) / Math.log(1024))
    const size = parseFloat((this.size / Math.pow(1024, i)).toFixed(2))

    return `${size} ${sizes[i]}`
  }

  /**
     * 获取分辨率
     */
  getResolution(): string {
    if (this.width === 0 || this.height === 0) return ''
    return `${this.width}x${this.height}`
  }

  /**
     * 将任务转换为JSON对象
     */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      team_id: this.team_id,
      group_id: this.group_id,
      make_cover_frame: this.make_cover_frame,
      make_track_frame: this.make_track_frame,
      need_bind: this.need_bind,
      path: this.path,
      uid: this.uid,
      url: this.url,
      script_id: this.script_id,
      resource_id: this.resource_id,
      cover: this.cover,
      cover_frame: this.cover_frame,
      track_frame: this.track_frame,
      name: this.name,
      codec_name: this.codec_name,
      hash: this.hash,
      duration: this.duration,
      width: this.width,
      height: this.height,
      size: this.size,
      status: this.status,
      progress: this.progress,
      folder_id: this.folder_id,
      upload_id: this.upload_id,
      type: this.type,
      reason: this.reason,
      task_nos: this.task_nos,
      extra_tasks: this.extra_tasks,
      upload_type: this.upload_type,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at,
      tag_id: this.tag_id,
      object_key: this.object_key,
      platform: this.platform
    }
  }
}
